#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط لنظام المحاسبة
"""

from database import AccountingDatabase
import pandas as pd
from datetime import date

def test_accounting_system():
    """اختبار شامل لنظام المحاسبة"""
    print("🧪 بدء اختبار نظام المحاسبة...")
    
    # إنشاء قاعدة البيانات
    db = AccountingDatabase("test_accounting.db")
    print("✅ تم إنشاء قاعدة البيانات")
    
    # اختبار إضافة حساب جديد
    result = db.add_account("1003", "المخزون", "أصول")
    print(f"✅ إضافة حساب جديد: {'نجح' if result else 'فشل'}")
    
    # اختبار إضافة صندوق جديد
    result = db.add_cash_box("CASH002", "صندوق المبيعات", 1000)
    print(f"✅ إضافة صندوق جديد: {'نجح' if result else 'فشل'}")
    
    # اختبار إضافة سلعة جديدة
    result = db.add_item("ITEM001", "لابتوب ديل", 15000, "لابتوب للمكتب")
    print(f"✅ إضافة سلعة جديدة: {'نجح' if result else 'فشل'}")
    
    # اختبار إضافة قيد محاسبي
    today = date.today().strftime('%Y-%m-%d')
    details = [
        {'account_code': '1001', 'debit': 5000, 'credit': 0},
        {'account_code': '3001', 'debit': 0, 'credit': 5000}
    ]
    result = db.add_journal_entry(today, "إيداع رأس مال نقدي", details)
    print(f"✅ إضافة قيد محاسبي: {'نجح' if result else 'فشل'}")
    
    # اختبار عرض البيانات
    print("\n📊 عرض البيانات:")
    
    # عرض الحسابات
    accounts = db.get_accounts()
    print(f"📋 عدد الحسابات: {len(accounts)}")
    print(accounts[['account_code', 'account_name', 'account_type']].to_string(index=False))
    
    # عرض الصناديق
    cash_boxes = db.get_cash_boxes()
    print(f"\n💰 عدد الصناديق: {len(cash_boxes)}")
    print(cash_boxes[['box_code', 'box_name', 'current_balance']].to_string(index=False))
    
    # عرض السلع
    items = db.get_items()
    print(f"\n📦 عدد السلع: {len(items)}")
    print(items[['item_code', 'item_name', 'item_price']].to_string(index=False))
    
    # اختبار كشف حساب
    print(f"\n📊 كشف حساب النقدية (1001):")
    statement = db.get_account_statement('1001')
    if not statement.empty:
        print(statement.to_string(index=False))
    else:
        print("لا توجد حركات")
    
    # اختبار رصيد الصندوق
    balance = db.get_cash_box_balance('CASH001')
    print(f"\n💰 رصيد الصندوق الرئيسي: {balance:,.2f}")
    
    print("\n🎉 انتهى الاختبار بنجاح!")

if __name__ == "__main__":
    test_accounting_system()
