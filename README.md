# نظام المحاسبة البسيط

نظام محاسبة شامل ومبسط تم تطويره باستخدام Python و Streamlit، يدعم نظام القيد المزدوج ويوفر جميع الميزات الأساسية للمحاسبة.

## الميزات الرئيسية

### 1. تسجيل العمليات اليومية
- نظام القيد المزدوج (مدين/دائن)
- التحقق التلقائي من توازن القيود
- إدخال متعدد البنود في القيد الواحد

### 2. إدارة الصناديق
- إضافة أكثر من صندوق نقدي
- متابعة أرصدة الصناديق
- تقارير مفصلة للصناديق

### 3. إدارة السلع والمصاريف
- إضافة سلع مع أكواد فريدة
- تسعير السلع
- وصف تفصيلي لكل سلعة

### 4. كشوف الحسابات
- كشف حساب مفصل لكل حساب
- عرض الرصيد الجاري
- تتبع جميع الحركات

### 5. عرض الأرصدة
- رصيد الصناديق الحالي
- أرصدة جميع الحسابات
- ملخص الوضع المالي

### 6. تصدير البيانات
- تصدير إلى ملفات Excel
- كشوف الحسابات
- قوائم السلع والحسابات
- ميزان المراجعة

### 7. واجهة مستخدم بسيطة
- واجهة ويب تفاعلية
- تصميم عربي مناسب
- سهولة في الاستخدام

## متطلبات النظام

- Python 3.7 أو أحدث
- المكتبات المطلوبة (موجودة في requirements.txt)

## التثبيت والتشغيل

### 1. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 2. تشغيل البرنامج
```bash
streamlit run main.py
```

### 3. فتح المتصفح
سيتم فتح البرنامج تلقائياً في المتصفح على العنوان:
```
http://localhost:8501
```

## هيكل المشروع

```
accounting-app/
├── main.py              # الملف الرئيسي للتطبيق
├── database.py          # إدارة قاعدة البيانات
├── utils.py             # الوظائف المساعدة
├── requirements.txt     # المتطلبات
├── README.md           # هذا الملف
└── accounting.db       # قاعدة البيانات (تُنشأ تلقائياً)
```

## كيفية الاستخدام

### 1. البدء
- عند تشغيل البرنامج لأول مرة، سيتم إنشاء قاعدة البيانات تلقائياً
- ستجد حسابات افتراضية جاهزة للاستخدام

### 2. إضافة الحسابات
- انتقل إلى "إدارة الحسابات"
- أضف الحسابات المطلوبة مع تحديد نوع كل حساب

### 3. إضافة الصناديق
- انتقل إلى "إدارة الصناديق"
- أضف الصناديق النقدية المختلفة

### 4. إضافة السلع
- انتقل إلى "إدارة السلع"
- أضف السلع مع أكوادها وأسعارها

### 5. تسجيل القيود
- انتقل إلى "تسجيل القيود"
- أدخل تفاصيل القيد مع التأكد من التوازن

### 6. مراجعة التقارير
- انتقل إلى "التقارير" لعرض ميزان المراجعة
- استخدم "كشوف الحسابات" لمراجعة حساب معين

## الحسابات الافتراضية

يتم إنشاء الحسابات التالية تلقائياً:

| الكود | اسم الحساب | النوع |
|-------|------------|-------|
| 1001 | النقدية | أصول |
| 1002 | البنك | أصول |
| 2001 | الموردون | خصوم |
| 3001 | رأس المال | حقوق ملكية |
| 4001 | المبيعات | إيرادات |
| 5001 | المشتريات | مصروفات |
| 5002 | مصروفات عمومية | مصروفات |

## أمثلة على القيود

### مثال 1: إيداع نقدي في رأس المال
```
التاريخ: 2024-01-01
الوصف: إيداع رأس مال نقدي

مدين: النقدية (1001) - 10,000
دائن: رأس المال (3001) - 10,000
```

### مثال 2: شراء سلعة نقداً
```
التاريخ: 2024-01-02
الوصف: شراء بضاعة نقداً

مدين: المشتريات (5001) - 5,000
دائن: النقدية (1001) - 5,000
```

## الدعم والمساعدة

- تأكد من توازن القيود قبل الحفظ
- استخدم البحث للعثور على الحسابات والسلع بسرعة
- قم بتصدير البيانات بانتظام كنسخة احتياطية
- راجع التقارير دورياً للتأكد من صحة البيانات

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.
