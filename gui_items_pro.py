#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إدارة السلع الاحترافية - ملف منفصل
"""

import tkinter as tk
from tkinter import ttk, messagebox

def show_items_pro(self):
    """عرض إدارة السلع الاحترافية"""
    self.clear_content()
    self.update_status("عرض إدارة السلع")
    
    # عنوان الصفحة
    title = tk.Label(self.content_frame, text="📦 إدارة السلع", 
                    font=('Arial', 18, 'bold'), bg='white', fg='#2c3e50')
    title.pack(pady=20)
    
    # شريط البحث
    search_var, search_entry = self.create_search_bar(self.content_frame, self.search_items)
    
    # أزرار العمليات
    add_btn, edit_btn, delete_btn, refresh_btn = self.create_action_buttons(
        self.content_frame, self.add_item_dialog, self.edit_item_dialog, self.delete_item
    )
    
    # جدول السلع
    items_frame = tk.LabelFrame(self.content_frame, text="قائمة السلع", 
                               font=('Arial', 12, 'bold'), bg='white')
    items_frame.pack(fill='both', expand=True, padx=20, pady=10)
    
    columns = ('كود السلعة', 'اسم السلعة', 'السعر', 'الوصف')
    self.items_tree = ttk.Treeview(items_frame, columns=columns, show='headings')
    
    # تنسيق الأعمدة
    for col in columns:
        self.items_tree.heading(col, text=col)
        if col == 'السعر':
            self.items_tree.column(col, width=120, anchor='e')
        elif col == 'الوصف':
            self.items_tree.column(col, width=250, anchor='w')
        else:
            self.items_tree.column(col, width=150, anchor='center')
    
    # ربط الأحداث
    self.items_tree.bind('<ButtonRelease-1>', self.on_item_select)
    self.items_tree.bind('<Double-1>', self.edit_item_dialog)
    
    self.items_tree.pack(fill='both', expand=True, padx=10, pady=10)
    
    # شريط التمرير
    scrollbar = ttk.Scrollbar(items_frame, orient='vertical', command=self.items_tree.yview)
    self.items_tree.configure(yscrollcommand=scrollbar.set)
    scrollbar.pack(side='right', fill='y')
    
    # إحصائيات السلع
    self.items_stats_label = tk.Label(self.content_frame, text="", 
                                     font=('Arial', 12, 'bold'), bg='white', fg='#e74c3c')
    self.items_stats_label.pack(pady=10)
    
    # تحديث البيانات
    self.refresh_items()
    
    # ربط زر التحديث
    refresh_btn.config(command=self.refresh_items)

def refresh_items(self):
    """تحديث قائمة السلع"""
    # مسح البيانات الحالية
    for item in self.items_tree.get_children():
        self.items_tree.delete(item)
    
    # إضافة البيانات الجديدة
    items_df = self.db.get_items()
    total_value = 0
    
    for _, row in items_df.iterrows():
        price = row['item_price']
        total_value += price
        self.items_tree.insert('', 'end', values=(
            row['item_code'], row['item_name'], 
            f"{price:,.2f}", row['item_description']
        ))
    
    # تحديث الإحصائيات
    self.items_stats_label.config(text=f"📦 إجمالي عدد السلع: {len(items_df)} | متوسط السعر: {total_value/len(items_df) if len(items_df) > 0 else 0:,.2f}")
    self.update_status(f"تم تحديث {len(items_df)} سلعة")

def search_items(self, search_term):
    """البحث في السلع"""
    # مسح البيانات الحالية
    for item in self.items_tree.get_children():
        self.items_tree.delete(item)
    
    # البحث وعرض النتائج
    items_df = self.db.get_items()
    if search_term:
        mask = (items_df['item_name'].str.contains(search_term, case=False, na=False) |
                items_df['item_code'].str.contains(search_term, case=False, na=False) |
                items_df['item_description'].str.contains(search_term, case=False, na=False))
        items_df = items_df[mask]
    
    total_value = 0
    for _, row in items_df.iterrows():
        price = row['item_price']
        total_value += price
        self.items_tree.insert('', 'end', values=(
            row['item_code'], row['item_name'], 
            f"{price:,.2f}", row['item_description']
        ))
    
    self.items_stats_label.config(text=f"📦 نتائج البحث: {len(items_df)} سلعة")
    self.update_status(f"تم العثور على {len(items_df)} سلعة")

def on_item_select(self, event):
    """عند تحديد سلعة"""
    selection = self.items_tree.selection()
    if selection:
        item = self.items_tree.item(selection[0])
        self.selected_item = item['values'][0]  # كود السلعة
        self.update_status(f"تم تحديد السلعة: {self.selected_item}")

def add_item_dialog(self):
    """نافذة إضافة سلعة جديدة"""
    dialog = tk.Toplevel(self.root)
    dialog.title("إضافة سلعة جديدة")
    dialog.geometry("600x500")
    dialog.configure(bg='white')
    dialog.transient(self.root)
    dialog.grab_set()
    
    # عنوان النافذة
    title = tk.Label(dialog, text="➕ إضافة سلعة جديدة", 
                    font=('Arial', 16, 'bold'), bg='white', fg='#2c3e50')
    title.pack(pady=20)
    
    # إطار الحقول
    fields_frame = tk.Frame(dialog, bg='white')
    fields_frame.pack(fill='both', expand=True, padx=30, pady=20)
    
    # حقول الإدخال
    tk.Label(fields_frame, text="كود السلعة:", font=('Arial', 12, 'bold'), bg='white').grid(row=0, column=0, sticky='w', pady=10)
    code_entry = tk.Entry(fields_frame, font=('Arial', 12), width=20)
    code_entry.grid(row=0, column=1, padx=10, pady=10, sticky='ew')
    
    tk.Label(fields_frame, text="اسم السلعة:", font=('Arial', 12, 'bold'), bg='white').grid(row=1, column=0, sticky='w', pady=10)
    name_entry = tk.Entry(fields_frame, font=('Arial', 12), width=30)
    name_entry.grid(row=1, column=1, padx=10, pady=10, sticky='ew')
    
    tk.Label(fields_frame, text="سعر السلعة:", font=('Arial', 12, 'bold'), bg='white').grid(row=2, column=0, sticky='w', pady=10)
    price_entry = tk.Entry(fields_frame, font=('Arial', 12), width=20)
    price_entry.insert(0, "0.0")
    price_entry.grid(row=2, column=1, padx=10, pady=10, sticky='ew')
    
    tk.Label(fields_frame, text="وصف السلعة:", font=('Arial', 12, 'bold'), bg='white').grid(row=3, column=0, sticky='nw', pady=10)
    desc_text = tk.Text(fields_frame, font=('Arial', 12), width=40, height=5)
    desc_text.grid(row=3, column=1, padx=10, pady=10, sticky='ew')
    
    fields_frame.grid_columnconfigure(1, weight=1)
    
    # أزرار العمليات
    buttons_frame = tk.Frame(dialog, bg='white')
    buttons_frame.pack(fill='x', padx=30, pady=20)
    
    def save_item():
        code = code_entry.get().strip()
        name = name_entry.get().strip()
        desc = desc_text.get("1.0", tk.END).strip()
        try:
            price = float(price_entry.get().strip())
        except ValueError:
            messagebox.showerror("خطأ", "السعر يجب أن يكون رقماً")
            return
        
        if not code or not name:
            messagebox.showerror("خطأ", "يرجى ملء الحقول المطلوبة")
            return
        
        if self.db.add_item(code, name, price, desc):
            messagebox.showinfo("نجح", "تم إضافة السلعة بنجاح")
            dialog.destroy()
            self.refresh_items()
        else:
            messagebox.showerror("خطأ", "كود السلعة موجود مسبقاً")
    
    save_btn = tk.Button(buttons_frame, text="💾 حفظ", command=save_item,
                        bg='#27ae60', fg='white', font=('Arial', 12, 'bold'),
                        relief='flat', padx=20, pady=8, cursor='hand2')
    save_btn.pack(side='left', padx=5)
    
    cancel_btn = tk.Button(buttons_frame, text="❌ إلغاء", command=dialog.destroy,
                          bg='#e74c3c', fg='white', font=('Arial', 12, 'bold'),
                          relief='flat', padx=20, pady=8, cursor='hand2')
    cancel_btn.pack(side='right', padx=5)
    
    # التركيز على أول حقل
    code_entry.focus()

def edit_item_dialog(self, event=None):
    """نافذة تعديل سلعة"""
    if not self.selected_item:
        messagebox.showwarning("تحذير", "يرجى تحديد سلعة للتعديل")
        return
    
    # الحصول على بيانات السلعة
    item_data = self.db.get_item_by_code(self.selected_item)
    if not item_data:
        messagebox.showerror("خطأ", "لم يتم العثور على السلعة")
        return
    
    dialog = tk.Toplevel(self.root)
    dialog.title("تعديل السلعة")
    dialog.geometry("600x500")
    dialog.configure(bg='white')
    dialog.transient(self.root)
    dialog.grab_set()
    
    # عنوان النافذة
    title = tk.Label(dialog, text="✏️ تعديل السلعة", 
                    font=('Arial', 16, 'bold'), bg='white', fg='#2c3e50')
    title.pack(pady=20)
    
    # إطار الحقول
    fields_frame = tk.Frame(dialog, bg='white')
    fields_frame.pack(fill='both', expand=True, padx=30, pady=20)
    
    # حقول الإدخال مع البيانات الحالية
    tk.Label(fields_frame, text="كود السلعة:", font=('Arial', 12, 'bold'), bg='white').grid(row=0, column=0, sticky='w', pady=10)
    code_entry = tk.Entry(fields_frame, font=('Arial', 12), width=20)
    code_entry.insert(0, item_data[0])  # item_code
    code_entry.grid(row=0, column=1, padx=10, pady=10, sticky='ew')
    
    tk.Label(fields_frame, text="اسم السلعة:", font=('Arial', 12, 'bold'), bg='white').grid(row=1, column=0, sticky='w', pady=10)
    name_entry = tk.Entry(fields_frame, font=('Arial', 12), width=30)
    name_entry.insert(0, item_data[1])  # item_name
    name_entry.grid(row=1, column=1, padx=10, pady=10, sticky='ew')
    
    tk.Label(fields_frame, text="سعر السلعة:", font=('Arial', 12, 'bold'), bg='white').grid(row=2, column=0, sticky='w', pady=10)
    price_entry = tk.Entry(fields_frame, font=('Arial', 12), width=20)
    price_entry.insert(0, str(item_data[2]))  # item_price
    price_entry.grid(row=2, column=1, padx=10, pady=10, sticky='ew')
    
    tk.Label(fields_frame, text="وصف السلعة:", font=('Arial', 12, 'bold'), bg='white').grid(row=3, column=0, sticky='nw', pady=10)
    desc_text = tk.Text(fields_frame, font=('Arial', 12), width=40, height=5)
    desc_text.insert("1.0", item_data[3])  # item_description
    desc_text.grid(row=3, column=1, padx=10, pady=10, sticky='ew')
    
    fields_frame.grid_columnconfigure(1, weight=1)
    
    # أزرار العمليات
    buttons_frame = tk.Frame(dialog, bg='white')
    buttons_frame.pack(fill='x', padx=30, pady=20)
    
    def update_item():
        new_code = code_entry.get().strip()
        name = name_entry.get().strip()
        desc = desc_text.get("1.0", tk.END).strip()
        try:
            price = float(price_entry.get().strip())
        except ValueError:
            messagebox.showerror("خطأ", "السعر يجب أن يكون رقماً")
            return
        
        if not new_code or not name:
            messagebox.showerror("خطأ", "يرجى ملء الحقول المطلوبة")
            return
        
        if self.db.update_item(self.selected_item, new_code, name, price, desc):
            messagebox.showinfo("نجح", "تم تحديث السلعة بنجاح")
            dialog.destroy()
            self.refresh_items()
            self.selected_item = new_code
        else:
            messagebox.showerror("خطأ", "فشل في تحديث السلعة")
    
    update_btn = tk.Button(buttons_frame, text="💾 تحديث", command=update_item,
                          bg='#f39c12', fg='white', font=('Arial', 12, 'bold'),
                          relief='flat', padx=20, pady=8, cursor='hand2')
    update_btn.pack(side='left', padx=5)
    
    cancel_btn = tk.Button(buttons_frame, text="❌ إلغاء", command=dialog.destroy,
                          bg='#e74c3c', fg='white', font=('Arial', 12, 'bold'),
                          relief='flat', padx=20, pady=8, cursor='hand2')
    cancel_btn.pack(side='right', padx=5)

def delete_item(self):
    """حذف سلعة"""
    if not self.selected_item:
        messagebox.showwarning("تحذير", "يرجى تحديد سلعة للحذف")
        return
    
    # تأكيد الحذف
    result = messagebox.askyesno("تأكيد الحذف", 
                               f"هل أنت متأكد من حذف السلعة '{self.selected_item}'؟\n"
                               "هذا الإجراء لا يمكن التراجع عنه!")
    
    if result:
        success, message = self.db.delete_item(self.selected_item)
        if success:
            messagebox.showinfo("نجح", message)
            self.refresh_items()
            self.selected_item = None
        else:
            messagebox.showerror("خطأ", message)
