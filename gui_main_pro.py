#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام المحاسبة البسيط - واجهة رسومية احترافية
الإصدار المحدث مع خصائص التعديل والحذف
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import pandas as pd
from datetime import datetime, date
from database import AccountingDatabase
from utils_gui import *
import os
import sys

class AccountingAppPro:
    def __init__(self, root):
        self.root = root
        self.root.title("نظام المحاسبة البسيط - الإصدار الاحترافي")
        self.root.geometry("1400x900")
        self.root.configure(bg='#f8f9fa')
        
        # تهيئة قاعدة البيانات
        try:
            self.db = AccountingDatabase()
            print("✅ تم تهيئة قاعدة البيانات بنجاح")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تهيئة قاعدة البيانات: {e}")
            sys.exit(1)
        
        # متغيرات للتحديد
        self.selected_account = None
        self.selected_cash_box = None
        self.selected_item = None
        self.selected_entry = None
        
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم الاحترافية"""
        # شريط العنوان
        self.create_header()
        
        # الإطار الرئيسي
        main_frame = tk.Frame(self.root, bg='#f8f9fa')
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # القائمة الجانبية
        self.create_sidebar(main_frame)
        
        # منطقة المحتوى الرئيسي
        self.content_frame = tk.Frame(main_frame, bg='white', relief='raised', bd=1)
        self.content_frame.pack(side='right', fill='both', expand=True, padx=(10, 0))
        
        # شريط الحالة
        self.create_status_bar()
        
        # عرض لوحة التحكم افتراضياً
        self.show_dashboard()
        
    def create_header(self):
        """إنشاء شريط العنوان الاحترافي"""
        header_frame = tk.Frame(self.root, bg='#2c3e50', height=80)
        header_frame.pack(fill='x', padx=10, pady=(10, 5))
        header_frame.pack_propagate(False)
        
        # العنوان الرئيسي
        title_label = tk.Label(header_frame, text="💰 نظام المحاسبة البسيط - الإصدار الاحترافي", 
                              font=('Arial', 18, 'bold'), fg='white', bg='#2c3e50')
        title_label.pack(side='left', padx=20, pady=20)
        
        # معلومات النظام
        info_frame = tk.Frame(header_frame, bg='#2c3e50')
        info_frame.pack(side='right', padx=20, pady=20)
        
        date_label = tk.Label(info_frame, text=f"التاريخ: {date.today().strftime('%Y-%m-%d')}", 
                             font=('Arial', 10), fg='white', bg='#2c3e50')
        date_label.pack()
        
        version_label = tk.Label(info_frame, text="الإصدار: 2.0 Pro", 
                               font=('Arial', 10), fg='white', bg='#2c3e50')
        version_label.pack()
        
    def create_sidebar(self, parent):
        """إنشاء القائمة الجانبية الاحترافية"""
        sidebar_frame = tk.Frame(parent, bg='#34495e', width=280)
        sidebar_frame.pack(side='left', fill='y')
        sidebar_frame.pack_propagate(False)
        
        # عنوان القائمة
        menu_title = tk.Label(sidebar_frame, text="القائمة الرئيسية", 
                             font=('Arial', 14, 'bold'), fg='white', bg='#34495e')
        menu_title.pack(pady=20)
        
        # أزرار القائمة المحدثة
        menu_buttons = [
            ("📊 لوحة التحكم", self.show_dashboard, "#3498db"),
            ("📋 إدارة الحسابات", self.show_accounts_pro, "#27ae60"),
            ("💰 إدارة الصناديق", self.show_cash_boxes_pro, "#f39c12"),
            ("📦 إدارة السلع", self.show_items_pro, "#e74c3c"),
            ("📝 إدارة القيود", self.show_journal_entries_pro, "#9b59b6"),
            ("📊 كشوف الحسابات", self.show_statements_pro, "#1abc9c"),
            ("📈 التقارير", self.show_reports_pro, "#34495e"),
            ("📤 تصدير البيانات", self.show_export_pro, "#95a5a6"),
            ("⚙️ الإعدادات", self.show_settings, "#7f8c8d")
        ]
        
        for text, command, color in menu_buttons:
            btn = tk.Button(sidebar_frame, text=text, command=command,
                           font=('Arial', 11, 'bold'), bg=color, fg='white',
                           relief='flat', pady=12, width=28, cursor='hand2',
                           activebackground=self.darken_color(color))
            btn.pack(fill='x', padx=15, pady=3)
            
            # تأثير hover
            btn.bind("<Enter>", lambda e, b=btn, c=color: b.config(bg=self.darken_color(c)))
            btn.bind("<Leave>", lambda e, b=btn, c=color: b.config(bg=c))
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_bar = tk.Label(self.root, text="جاهز", relief=tk.SUNKEN, 
                                  anchor=tk.W, bg='#ecf0f1', font=('Arial', 9))
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=(0, 10))
        
    def update_status(self, message):
        """تحديث شريط الحالة"""
        self.status_bar.config(text=f"{datetime.now().strftime('%H:%M:%S')} - {message}")
        self.root.update()
        
    def darken_color(self, color):
        """تغميق اللون للتأثيرات"""
        color_map = {
            "#3498db": "#2980b9",
            "#27ae60": "#229954",
            "#f39c12": "#e67e22",
            "#e74c3c": "#c0392b",
            "#9b59b6": "#8e44ad",
            "#1abc9c": "#16a085",
            "#34495e": "#2c3e50",
            "#95a5a6": "#7f8c8d",
            "#7f8c8d": "#6c7b7d"
        }
        return color_map.get(color, color)
        
    def clear_content(self):
        """مسح المحتوى الحالي"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()
    
    def create_action_buttons(self, parent, add_func, edit_func, delete_func):
        """إنشاء أزرار العمليات الموحدة"""
        buttons_frame = tk.Frame(parent, bg='white')
        buttons_frame.pack(fill='x', padx=20, pady=10)
        
        # زر الإضافة
        add_btn = tk.Button(buttons_frame, text="➕ إضافة جديد", command=add_func,
                           bg='#27ae60', fg='white', font=('Arial', 11, 'bold'),
                           relief='flat', padx=20, pady=8, cursor='hand2')
        add_btn.pack(side='left', padx=5)
        
        # زر التعديل
        edit_btn = tk.Button(buttons_frame, text="✏️ تعديل", command=edit_func,
                            bg='#f39c12', fg='white', font=('Arial', 11, 'bold'),
                            relief='flat', padx=20, pady=8, cursor='hand2')
        edit_btn.pack(side='left', padx=5)
        
        # زر الحذف
        delete_btn = tk.Button(buttons_frame, text="🗑️ حذف", command=delete_func,
                              bg='#e74c3c', fg='white', font=('Arial', 11, 'bold'),
                              relief='flat', padx=20, pady=8, cursor='hand2')
        delete_btn.pack(side='left', padx=5)
        
        # زر التحديث
        refresh_btn = tk.Button(buttons_frame, text="🔄 تحديث", command=lambda: self.update_status("تم تحديث البيانات"),
                               bg='#3498db', fg='white', font=('Arial', 11, 'bold'),
                               relief='flat', padx=20, pady=8, cursor='hand2')
        refresh_btn.pack(side='right', padx=5)
        
        return add_btn, edit_btn, delete_btn, refresh_btn
    
    def create_search_bar(self, parent, search_func):
        """إنشاء شريط البحث"""
        search_frame = tk.Frame(parent, bg='white')
        search_frame.pack(fill='x', padx=20, pady=10)
        
        tk.Label(search_frame, text="🔍 البحث:", font=('Arial', 11, 'bold'), bg='white').pack(side='left', padx=5)
        
        search_var = tk.StringVar()
        search_entry = tk.Entry(search_frame, textvariable=search_var, font=('Arial', 11), width=30)
        search_entry.pack(side='left', padx=5)
        
        search_btn = tk.Button(search_frame, text="بحث", command=lambda: search_func(search_var.get()),
                              bg='#3498db', fg='white', font=('Arial', 10, 'bold'),
                              relief='flat', padx=15, pady=5, cursor='hand2')
        search_btn.pack(side='left', padx=5)
        
        # البحث التلقائي عند الكتابة
        search_var.trace('w', lambda *args: search_func(search_var.get()))
        
        return search_var, search_entry
    
    def show_dashboard(self):
        """عرض لوحة التحكم المحدثة"""
        self.clear_content()
        self.update_status("عرض لوحة التحكم")
        
        # عنوان الصفحة
        title = tk.Label(self.content_frame, text="📊 لوحة التحكم", 
                        font=('Arial', 20, 'bold'), bg='white', fg='#2c3e50')
        title.pack(pady=30)
        
        # إطار الإحصائيات المحدث
        stats_frame = tk.Frame(self.content_frame, bg='white')
        stats_frame.pack(fill='x', padx=30, pady=20)
        
        # الحصول على الإحصائيات
        accounts_count = len(self.db.get_accounts())
        cash_boxes = self.db.get_cash_boxes()
        total_cash = cash_boxes['current_balance'].sum()
        items_count = len(self.db.get_items())
        
        # عدد القيود اليوم
        today = date.today().strftime('%Y-%m-%d')
        conn = self.db.get_connection()
        today_entries = pd.read_sql_query(
            "SELECT COUNT(*) as count FROM journal_entries WHERE entry_date = ?", 
            conn, params=[today]
        )
        conn.close()
        
        # عرض الإحصائيات بتصميم احترافي
        stats = [
            ("عدد الحسابات", accounts_count, "#3498db", "📋"),
            ("إجمالي النقدية", f"{total_cash:,.2f}", "#27ae60", "💰"),
            ("عدد السلع", items_count, "#e74c3c", "📦"),
            ("قيود اليوم", today_entries['count'].iloc[0], "#9b59b6", "📝")
        ]
        
        for i, (label, value, color, icon) in enumerate(stats):
            stat_frame = tk.Frame(stats_frame, bg=color, relief='raised', bd=3)
            stat_frame.grid(row=0, column=i, padx=15, pady=10, sticky='ew', ipadx=20, ipady=20)
            stats_frame.grid_columnconfigure(i, weight=1)
            
            # الأيقونة
            icon_label = tk.Label(stat_frame, text=icon, font=('Arial', 24), 
                                 fg='white', bg=color)
            icon_label.pack(pady=(10, 5))
            
            # القيمة
            value_label = tk.Label(stat_frame, text=str(value), font=('Arial', 18, 'bold'), 
                                  fg='white', bg=color)
            value_label.pack()
            
            # التسمية
            label_label = tk.Label(stat_frame, text=label, font=('Arial', 12), 
                                  fg='white', bg=color)
            label_label.pack(pady=(5, 10))
        
        # آخر القيود مع تصميم محدث
        recent_frame = tk.LabelFrame(self.content_frame, text="📝 آخر القيود المحاسبية", 
                                   font=('Arial', 14, 'bold'), bg='white', fg='#2c3e50')
        recent_frame.pack(fill='both', expand=True, padx=30, pady=20)
        
        # جدول آخر القيود مع تحسينات
        columns = ('رقم القيد', 'التاريخ', 'الوصف', 'المبلغ')
        tree = ttk.Treeview(recent_frame, columns=columns, show='headings', height=12)
        
        # تنسيق الأعمدة
        tree.heading('رقم القيد', text='رقم القيد')
        tree.heading('التاريخ', text='التاريخ')
        tree.heading('الوصف', text='الوصف')
        tree.heading('المبلغ', text='إجمالي المبلغ')
        
        tree.column('رقم القيد', width=100, anchor='center')
        tree.column('التاريخ', width=120, anchor='center')
        tree.column('الوصف', width=300, anchor='w')
        tree.column('المبلغ', width=150, anchor='e')
        
        # الحصول على آخر القيود مع المبالغ
        conn = self.db.get_connection()
        recent_entries = pd.read_sql_query(
            """SELECT je.entry_id, je.entry_date, je.description,
                      SUM(ed.debit_amount) as total_amount
               FROM journal_entries je
               LEFT JOIN entry_details ed ON je.entry_id = ed.entry_id
               GROUP BY je.entry_id, je.entry_date, je.description
               ORDER BY je.entry_id DESC 
               LIMIT 10""", 
            conn
        )
        conn.close()
        
        # إضافة البيانات مع تنسيق
        for _, row in recent_entries.iterrows():
            amount_formatted = f"{row['total_amount']:,.2f}" if pd.notna(row['total_amount']) else "0.00"
            tree.insert('', 'end', values=(
                row['entry_id'], 
                row['entry_date'], 
                row['description'][:50] + "..." if len(row['description']) > 50 else row['description'],
                amount_formatted
            ))
        
        tree.pack(fill='both', expand=True, padx=15, pady=15)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(recent_frame, orient='vertical', command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side='right', fill='y')
        
        # رسالة ترحيب
        if recent_entries.empty:
            welcome_label = tk.Label(recent_frame, text="مرحباً بك في نظام المحاسبة البسيط!\nابدأ بإضافة الحسابات والقيود المحاسبية",
                                   font=('Arial', 12), bg='white', fg='#7f8c8d')
            welcome_label.pack(pady=50)

    def show_accounts_pro(self):
        """عرض إدارة الحسابات الاحترافية"""
        self.clear_content()
        self.update_status("عرض إدارة الحسابات")

        # عنوان الصفحة
        title = tk.Label(self.content_frame, text="📋 إدارة الحسابات",
                        font=('Arial', 18, 'bold'), bg='white', fg='#2c3e50')
        title.pack(pady=20)

        # شريط البحث
        search_var, search_entry = self.create_search_bar(self.content_frame, self.search_accounts)

        # أزرار العمليات
        add_btn, edit_btn, delete_btn, refresh_btn = self.create_action_buttons(
            self.content_frame, self.add_account_dialog, self.edit_account_dialog, self.delete_account
        )

        # جدول الحسابات
        accounts_frame = tk.LabelFrame(self.content_frame, text="قائمة الحسابات",
                                     font=('Arial', 12, 'bold'), bg='white')
        accounts_frame.pack(fill='both', expand=True, padx=20, pady=10)

        columns = ('كود الحساب', 'اسم الحساب', 'نوع الحساب', 'تاريخ الإنشاء')
        self.accounts_tree = ttk.Treeview(accounts_frame, columns=columns, show='headings')

        # تنسيق الأعمدة
        for col in columns:
            self.accounts_tree.heading(col, text=col)
            self.accounts_tree.column(col, width=200, anchor='center')

        # ربط الأحداث
        self.accounts_tree.bind('<ButtonRelease-1>', self.on_account_select)
        self.accounts_tree.bind('<Double-1>', self.edit_account_dialog)

        self.accounts_tree.pack(fill='both', expand=True, padx=10, pady=10)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(accounts_frame, orient='vertical', command=self.accounts_tree.yview)
        self.accounts_tree.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side='right', fill='y')

        # تحديث البيانات
        self.refresh_accounts()

        # ربط زر التحديث
        refresh_btn.config(command=self.refresh_accounts)

    def refresh_accounts(self):
        """تحديث قائمة الحسابات"""
        # مسح البيانات الحالية
        for item in self.accounts_tree.get_children():
            self.accounts_tree.delete(item)

        # إضافة البيانات الجديدة
        accounts_df = self.db.get_accounts()
        for _, row in accounts_df.iterrows():
            self.accounts_tree.insert('', 'end', values=(
                row['account_code'], row['account_name'],
                row['account_type'], row['created_date'][:10]
            ))

        self.update_status(f"تم تحديث {len(accounts_df)} حساب")

    def search_accounts(self, search_term):
        """البحث في الحسابات"""
        # مسح البيانات الحالية
        for item in self.accounts_tree.get_children():
            self.accounts_tree.delete(item)

        # البحث وعرض النتائج
        accounts_df = search_accounts(self.db, search_term)
        for _, row in accounts_df.iterrows():
            self.accounts_tree.insert('', 'end', values=(
                row['account_code'], row['account_name'],
                row['account_type'], row['created_date'][:10]
            ))

        self.update_status(f"تم العثور على {len(accounts_df)} حساب")

    def on_account_select(self, event):
        """عند تحديد حساب"""
        selection = self.accounts_tree.selection()
        if selection:
            item = self.accounts_tree.item(selection[0])
            self.selected_account = item['values'][0]  # كود الحساب
            self.update_status(f"تم تحديد الحساب: {self.selected_account}")

    def add_account_dialog(self):
        """نافذة إضافة حساب جديد"""
        dialog = tk.Toplevel(self.root)
        dialog.title("إضافة حساب جديد")
        dialog.geometry("500x400")
        dialog.configure(bg='white')
        dialog.transient(self.root)
        dialog.grab_set()

        # عنوان النافذة
        title = tk.Label(dialog, text="➕ إضافة حساب جديد",
                        font=('Arial', 16, 'bold'), bg='white', fg='#2c3e50')
        title.pack(pady=20)

        # إطار الحقول
        fields_frame = tk.Frame(dialog, bg='white')
        fields_frame.pack(fill='both', expand=True, padx=30, pady=20)

        # حقول الإدخال
        tk.Label(fields_frame, text="كود الحساب:", font=('Arial', 12, 'bold'), bg='white').grid(row=0, column=0, sticky='w', pady=10)
        code_entry = tk.Entry(fields_frame, font=('Arial', 12), width=20)
        code_entry.grid(row=0, column=1, padx=10, pady=10, sticky='ew')

        tk.Label(fields_frame, text="اسم الحساب:", font=('Arial', 12, 'bold'), bg='white').grid(row=1, column=0, sticky='w', pady=10)
        name_entry = tk.Entry(fields_frame, font=('Arial', 12), width=30)
        name_entry.grid(row=1, column=1, padx=10, pady=10, sticky='ew')

        tk.Label(fields_frame, text="نوع الحساب:", font=('Arial', 12, 'bold'), bg='white').grid(row=2, column=0, sticky='w', pady=10)
        type_combo = ttk.Combobox(fields_frame, values=get_account_types(), font=('Arial', 12), width=18)
        type_combo.grid(row=2, column=1, padx=10, pady=10, sticky='ew')

        fields_frame.grid_columnconfigure(1, weight=1)

        # أزرار العمليات
        buttons_frame = tk.Frame(dialog, bg='white')
        buttons_frame.pack(fill='x', padx=30, pady=20)

        def save_account():
            code = code_entry.get().strip()
            name = name_entry.get().strip()
            acc_type = type_combo.get().strip()

            if not code or not name or not acc_type:
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول")
                return

            if self.db.add_account(code, name, acc_type):
                messagebox.showinfo("نجح", "تم إضافة الحساب بنجاح")
                dialog.destroy()
                self.refresh_accounts()
            else:
                messagebox.showerror("خطأ", "كود الحساب موجود مسبقاً")

        save_btn = tk.Button(buttons_frame, text="💾 حفظ", command=save_account,
                            bg='#27ae60', fg='white', font=('Arial', 12, 'bold'),
                            relief='flat', padx=20, pady=8, cursor='hand2')
        save_btn.pack(side='left', padx=5)

        cancel_btn = tk.Button(buttons_frame, text="❌ إلغاء", command=dialog.destroy,
                              bg='#e74c3c', fg='white', font=('Arial', 12, 'bold'),
                              relief='flat', padx=20, pady=8, cursor='hand2')
        cancel_btn.pack(side='right', padx=5)

        # التركيز على أول حقل
        code_entry.focus()

    def edit_account_dialog(self, event=None):
        """نافذة تعديل حساب"""
        if not self.selected_account:
            messagebox.showwarning("تحذير", "يرجى تحديد حساب للتعديل")
            return

        # الحصول على بيانات الحساب
        account_data = self.db.get_account_by_code(self.selected_account)
        if not account_data:
            messagebox.showerror("خطأ", "لم يتم العثور على الحساب")
            return

        dialog = tk.Toplevel(self.root)
        dialog.title("تعديل الحساب")
        dialog.geometry("500x400")
        dialog.configure(bg='white')
        dialog.transient(self.root)
        dialog.grab_set()

        # عنوان النافذة
        title = tk.Label(dialog, text="✏️ تعديل الحساب",
                        font=('Arial', 16, 'bold'), bg='white', fg='#2c3e50')
        title.pack(pady=20)

        # إطار الحقول
        fields_frame = tk.Frame(dialog, bg='white')
        fields_frame.pack(fill='both', expand=True, padx=30, pady=20)

        # حقول الإدخال مع البيانات الحالية
        tk.Label(fields_frame, text="كود الحساب:", font=('Arial', 12, 'bold'), bg='white').grid(row=0, column=0, sticky='w', pady=10)
        code_entry = tk.Entry(fields_frame, font=('Arial', 12), width=20)
        code_entry.insert(0, account_data[0])  # account_code
        code_entry.grid(row=0, column=1, padx=10, pady=10, sticky='ew')

        tk.Label(fields_frame, text="اسم الحساب:", font=('Arial', 12, 'bold'), bg='white').grid(row=1, column=0, sticky='w', pady=10)
        name_entry = tk.Entry(fields_frame, font=('Arial', 12), width=30)
        name_entry.insert(0, account_data[1])  # account_name
        name_entry.grid(row=1, column=1, padx=10, pady=10, sticky='ew')

        tk.Label(fields_frame, text="نوع الحساب:", font=('Arial', 12, 'bold'), bg='white').grid(row=2, column=0, sticky='w', pady=10)
        type_combo = ttk.Combobox(fields_frame, values=get_account_types(), font=('Arial', 12), width=18)
        type_combo.set(account_data[2])  # account_type
        type_combo.grid(row=2, column=1, padx=10, pady=10, sticky='ew')

        fields_frame.grid_columnconfigure(1, weight=1)

        # أزرار العمليات
        buttons_frame = tk.Frame(dialog, bg='white')
        buttons_frame.pack(fill='x', padx=30, pady=20)

        def update_account():
            new_code = code_entry.get().strip()
            name = name_entry.get().strip()
            acc_type = type_combo.get().strip()

            if not new_code or not name or not acc_type:
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول")
                return

            if self.db.update_account(self.selected_account, new_code, name, acc_type):
                messagebox.showinfo("نجح", "تم تحديث الحساب بنجاح")
                dialog.destroy()
                self.refresh_accounts()
                self.selected_account = new_code
            else:
                messagebox.showerror("خطأ", "فشل في تحديث الحساب")

        update_btn = tk.Button(buttons_frame, text="💾 تحديث", command=update_account,
                              bg='#f39c12', fg='white', font=('Arial', 12, 'bold'),
                              relief='flat', padx=20, pady=8, cursor='hand2')
        update_btn.pack(side='left', padx=5)

        cancel_btn = tk.Button(buttons_frame, text="❌ إلغاء", command=dialog.destroy,
                              bg='#e74c3c', fg='white', font=('Arial', 12, 'bold'),
                              relief='flat', padx=20, pady=8, cursor='hand2')
        cancel_btn.pack(side='right', padx=5)

    def delete_account(self):
        """حذف حساب"""
        if not self.selected_account:
            messagebox.showwarning("تحذير", "يرجى تحديد حساب للحذف")
            return

        # تأكيد الحذف
        result = messagebox.askyesno("تأكيد الحذف",
                                   f"هل أنت متأكد من حذف الحساب '{self.selected_account}'؟\n"
                                   "هذا الإجراء لا يمكن التراجع عنه!")

        if result:
            success, message = self.db.delete_account(self.selected_account)
            if success:
                messagebox.showinfo("نجح", message)
                self.refresh_accounts()
                self.selected_account = None
            else:
                messagebox.showerror("خطأ", message)

    # ==================== إدارة الصناديق الاحترافية ====================

    def show_cash_boxes_pro(self):
        """عرض إدارة الصناديق الاحترافية"""
        self.clear_content()
        self.update_status("عرض إدارة الصناديق")

        # عنوان الصفحة
        title = tk.Label(self.content_frame, text="💰 إدارة الصناديق",
                        font=('Arial', 18, 'bold'), bg='white', fg='#2c3e50')
        title.pack(pady=20)

        # شريط البحث
        search_var, search_entry = self.create_search_bar(self.content_frame, self.search_cash_boxes)

        # أزرار العمليات
        add_btn, edit_btn, delete_btn, refresh_btn = self.create_action_buttons(
            self.content_frame, self.add_cash_box_dialog, self.edit_cash_box_dialog, self.delete_cash_box
        )

        # جدول الصناديق
        boxes_frame = tk.LabelFrame(self.content_frame, text="قائمة الصناديق",
                                   font=('Arial', 12, 'bold'), bg='white')
        boxes_frame.pack(fill='both', expand=True, padx=20, pady=10)

        columns = ('كود الصندوق', 'اسم الصندوق', 'الرصيد الحالي', 'تاريخ الإنشاء')
        self.cash_boxes_tree = ttk.Treeview(boxes_frame, columns=columns, show='headings')

        # تنسيق الأعمدة
        for col in columns:
            self.cash_boxes_tree.heading(col, text=col)
            if col == 'الرصيد الحالي':
                self.cash_boxes_tree.column(col, width=150, anchor='e')
            else:
                self.cash_boxes_tree.column(col, width=200, anchor='center')

        # ربط الأحداث
        self.cash_boxes_tree.bind('<ButtonRelease-1>', self.on_cash_box_select)
        self.cash_boxes_tree.bind('<Double-1>', self.edit_cash_box_dialog)

        self.cash_boxes_tree.pack(fill='both', expand=True, padx=10, pady=10)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(boxes_frame, orient='vertical', command=self.cash_boxes_tree.yview)
        self.cash_boxes_tree.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side='right', fill='y')

        # إجمالي الأرصدة
        self.total_cash_label = tk.Label(self.content_frame, text="",
                                        font=('Arial', 14, 'bold'), bg='white', fg='#27ae60')
        self.total_cash_label.pack(pady=10)

        # تحديث البيانات
        self.refresh_cash_boxes()

        # ربط زر التحديث
        refresh_btn.config(command=self.refresh_cash_boxes)

    def refresh_cash_boxes(self):
        """تحديث قائمة الصناديق"""
        # مسح البيانات الحالية
        for item in self.cash_boxes_tree.get_children():
            self.cash_boxes_tree.delete(item)

        # إضافة البيانات الجديدة
        boxes_df = self.db.get_cash_boxes()
        total_balance = 0

        for _, row in boxes_df.iterrows():
            balance = row['current_balance']
            total_balance += balance
            self.cash_boxes_tree.insert('', 'end', values=(
                row['box_code'], row['box_name'],
                f"{balance:,.2f}", row['created_date'][:10]
            ))

        # تحديث الإجمالي
        self.total_cash_label.config(text=f"💰 إجمالي أرصدة الصناديق: {total_balance:,.2f}")
        self.update_status(f"تم تحديث {len(boxes_df)} صندوق")

    def search_cash_boxes(self, search_term):
        """البحث في الصناديق"""
        # مسح البيانات الحالية
        for item in self.cash_boxes_tree.get_children():
            self.cash_boxes_tree.delete(item)

        # البحث وعرض النتائج
        boxes_df = self.db.get_cash_boxes()
        if search_term:
            mask = (boxes_df['box_name'].str.contains(search_term, case=False, na=False) |
                    boxes_df['box_code'].str.contains(search_term, case=False, na=False))
            boxes_df = boxes_df[mask]

        total_balance = 0
        for _, row in boxes_df.iterrows():
            balance = row['current_balance']
            total_balance += balance
            self.cash_boxes_tree.insert('', 'end', values=(
                row['box_code'], row['box_name'],
                f"{balance:,.2f}", row['created_date'][:10]
            ))

        self.total_cash_label.config(text=f"💰 إجمالي أرصدة الصناديق: {total_balance:,.2f}")
        self.update_status(f"تم العثور على {len(boxes_df)} صندوق")

    def on_cash_box_select(self, event):
        """عند تحديد صندوق"""
        selection = self.cash_boxes_tree.selection()
        if selection:
            item = self.cash_boxes_tree.item(selection[0])
            self.selected_cash_box = item['values'][0]  # كود الصندوق
            self.update_status(f"تم تحديد الصندوق: {self.selected_cash_box}")

    def add_cash_box_dialog(self):
        """نافذة إضافة صندوق جديد"""
        dialog = tk.Toplevel(self.root)
        dialog.title("إضافة صندوق جديد")
        dialog.geometry("500x400")
        dialog.configure(bg='white')
        dialog.transient(self.root)
        dialog.grab_set()

        # عنوان النافذة
        title = tk.Label(dialog, text="➕ إضافة صندوق جديد",
                        font=('Arial', 16, 'bold'), bg='white', fg='#2c3e50')
        title.pack(pady=20)

        # إطار الحقول
        fields_frame = tk.Frame(dialog, bg='white')
        fields_frame.pack(fill='both', expand=True, padx=30, pady=20)

        # حقول الإدخال
        tk.Label(fields_frame, text="كود الصندوق:", font=('Arial', 12, 'bold'), bg='white').grid(row=0, column=0, sticky='w', pady=10)
        code_entry = tk.Entry(fields_frame, font=('Arial', 12), width=20)
        code_entry.grid(row=0, column=1, padx=10, pady=10, sticky='ew')

        tk.Label(fields_frame, text="اسم الصندوق:", font=('Arial', 12, 'bold'), bg='white').grid(row=1, column=0, sticky='w', pady=10)
        name_entry = tk.Entry(fields_frame, font=('Arial', 12), width=30)
        name_entry.grid(row=1, column=1, padx=10, pady=10, sticky='ew')

        tk.Label(fields_frame, text="الرصيد الابتدائي:", font=('Arial', 12, 'bold'), bg='white').grid(row=2, column=0, sticky='w', pady=10)
        balance_entry = tk.Entry(fields_frame, font=('Arial', 12), width=20)
        balance_entry.insert(0, "0.0")
        balance_entry.grid(row=2, column=1, padx=10, pady=10, sticky='ew')

        fields_frame.grid_columnconfigure(1, weight=1)

        # أزرار العمليات
        buttons_frame = tk.Frame(dialog, bg='white')
        buttons_frame.pack(fill='x', padx=30, pady=20)

        def save_cash_box():
            code = code_entry.get().strip()
            name = name_entry.get().strip()
            try:
                balance = float(balance_entry.get().strip())
            except ValueError:
                messagebox.showerror("خطأ", "الرصيد الابتدائي يجب أن يكون رقماً")
                return

            if not code or not name:
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول")
                return

            if self.db.add_cash_box(code, name, balance):
                messagebox.showinfo("نجح", "تم إضافة الصندوق بنجاح")
                dialog.destroy()
                self.refresh_cash_boxes()
            else:
                messagebox.showerror("خطأ", "كود الصندوق موجود مسبقاً")

        save_btn = tk.Button(buttons_frame, text="💾 حفظ", command=save_cash_box,
                            bg='#27ae60', fg='white', font=('Arial', 12, 'bold'),
                            relief='flat', padx=20, pady=8, cursor='hand2')
        save_btn.pack(side='left', padx=5)

        cancel_btn = tk.Button(buttons_frame, text="❌ إلغاء", command=dialog.destroy,
                              bg='#e74c3c', fg='white', font=('Arial', 12, 'bold'),
                              relief='flat', padx=20, pady=8, cursor='hand2')
        cancel_btn.pack(side='right', padx=5)

        # التركيز على أول حقل
        code_entry.focus()

    def edit_cash_box_dialog(self, event=None):
        """نافذة تعديل صندوق"""
        if not self.selected_cash_box:
            messagebox.showwarning("تحذير", "يرجى تحديد صندوق للتعديل")
            return

        # الحصول على بيانات الصندوق
        box_data = self.db.get_cash_box_by_code(self.selected_cash_box)
        if not box_data:
            messagebox.showerror("خطأ", "لم يتم العثور على الصندوق")
            return

        dialog = tk.Toplevel(self.root)
        dialog.title("تعديل الصندوق")
        dialog.geometry("500x400")
        dialog.configure(bg='white')
        dialog.transient(self.root)
        dialog.grab_set()

        # عنوان النافذة
        title = tk.Label(dialog, text="✏️ تعديل الصندوق",
                        font=('Arial', 16, 'bold'), bg='white', fg='#2c3e50')
        title.pack(pady=20)

        # إطار الحقول
        fields_frame = tk.Frame(dialog, bg='white')
        fields_frame.pack(fill='both', expand=True, padx=30, pady=20)

        # حقول الإدخال مع البيانات الحالية
        tk.Label(fields_frame, text="كود الصندوق:", font=('Arial', 12, 'bold'), bg='white').grid(row=0, column=0, sticky='w', pady=10)
        code_entry = tk.Entry(fields_frame, font=('Arial', 12), width=20)
        code_entry.insert(0, box_data[0])  # box_code
        code_entry.grid(row=0, column=1, padx=10, pady=10, sticky='ew')

        tk.Label(fields_frame, text="اسم الصندوق:", font=('Arial', 12, 'bold'), bg='white').grid(row=1, column=0, sticky='w', pady=10)
        name_entry = tk.Entry(fields_frame, font=('Arial', 12), width=30)
        name_entry.insert(0, box_data[1])  # box_name
        name_entry.grid(row=1, column=1, padx=10, pady=10, sticky='ew')

        tk.Label(fields_frame, text="الرصيد الحالي:", font=('Arial', 12, 'bold'), bg='white').grid(row=2, column=0, sticky='w', pady=10)
        balance_entry = tk.Entry(fields_frame, font=('Arial', 12), width=20)
        balance_entry.insert(0, str(box_data[2]))  # current_balance
        balance_entry.grid(row=2, column=1, padx=10, pady=10, sticky='ew')

        fields_frame.grid_columnconfigure(1, weight=1)

        # أزرار العمليات
        buttons_frame = tk.Frame(dialog, bg='white')
        buttons_frame.pack(fill='x', padx=30, pady=20)

        def update_cash_box():
            new_code = code_entry.get().strip()
            name = name_entry.get().strip()
            try:
                balance = float(balance_entry.get().strip())
            except ValueError:
                messagebox.showerror("خطأ", "الرصيد يجب أن يكون رقماً")
                return

            if not new_code or not name:
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول")
                return

            if self.db.update_cash_box(self.selected_cash_box, new_code, name, balance):
                messagebox.showinfo("نجح", "تم تحديث الصندوق بنجاح")
                dialog.destroy()
                self.refresh_cash_boxes()
                self.selected_cash_box = new_code
            else:
                messagebox.showerror("خطأ", "فشل في تحديث الصندوق")

        update_btn = tk.Button(buttons_frame, text="💾 تحديث", command=update_cash_box,
                              bg='#f39c12', fg='white', font=('Arial', 12, 'bold'),
                              relief='flat', padx=20, pady=8, cursor='hand2')
        update_btn.pack(side='left', padx=5)

        cancel_btn = tk.Button(buttons_frame, text="❌ إلغاء", command=dialog.destroy,
                              bg='#e74c3c', fg='white', font=('Arial', 12, 'bold'),
                              relief='flat', padx=20, pady=8, cursor='hand2')
        cancel_btn.pack(side='right', padx=5)

    def delete_cash_box(self):
        """حذف صندوق"""
        if not self.selected_cash_box:
            messagebox.showwarning("تحذير", "يرجى تحديد صندوق للحذف")
            return

        # تأكيد الحذف
        result = messagebox.askyesno("تأكيد الحذف",
                                   f"هل أنت متأكد من حذف الصندوق '{self.selected_cash_box}'؟\n"
                                   "هذا الإجراء لا يمكن التراجع عنه!")

        if result:
            success, message = self.db.delete_cash_box(self.selected_cash_box)
            if success:
                messagebox.showinfo("نجح", message)
                self.refresh_cash_boxes()
                self.selected_cash_box = None
            else:
                messagebox.showerror("خطأ", message)

    # ==================== الوظائف المؤقتة للصفحات المتبقية ====================

    def show_items_pro(self):
        """عرض إدارة السلع الاحترافية"""
        self.clear_content()
        self.update_status("عرض إدارة السلع")

        # عنوان الصفحة
        title = tk.Label(self.content_frame, text="📦 إدارة السلع",
                        font=('Arial', 18, 'bold'), bg='white', fg='#2c3e50')
        title.pack(pady=20)

        # شريط البحث
        search_var, search_entry = self.create_search_bar(self.content_frame, self.search_items)

        # أزرار العمليات
        add_btn, edit_btn, delete_btn, refresh_btn = self.create_action_buttons(
            self.content_frame, self.add_item_dialog, self.edit_item_dialog, self.delete_item
        )

        # جدول السلع
        items_frame = tk.LabelFrame(self.content_frame, text="قائمة السلع",
                                   font=('Arial', 12, 'bold'), bg='white')
        items_frame.pack(fill='both', expand=True, padx=20, pady=10)

        columns = ('كود السلعة', 'اسم السلعة', 'السعر', 'الوصف')
        self.items_tree = ttk.Treeview(items_frame, columns=columns, show='headings')

        # تنسيق الأعمدة
        for col in columns:
            self.items_tree.heading(col, text=col)
            if col == 'السعر':
                self.items_tree.column(col, width=120, anchor='e')
            elif col == 'الوصف':
                self.items_tree.column(col, width=250, anchor='w')
            else:
                self.items_tree.column(col, width=150, anchor='center')

        # ربط الأحداث
        self.items_tree.bind('<ButtonRelease-1>', self.on_item_select)
        self.items_tree.bind('<Double-1>', self.edit_item_dialog)

        self.items_tree.pack(fill='both', expand=True, padx=10, pady=10)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(items_frame, orient='vertical', command=self.items_tree.yview)
        self.items_tree.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side='right', fill='y')

        # إحصائيات السلع
        self.items_stats_label = tk.Label(self.content_frame, text="",
                                         font=('Arial', 12, 'bold'), bg='white', fg='#e74c3c')
        self.items_stats_label.pack(pady=10)

        # تحديث البيانات
        self.refresh_items()

        # ربط زر التحديث
        refresh_btn.config(command=self.refresh_items)

    def refresh_items(self):
        """تحديث قائمة السلع"""
        # مسح البيانات الحالية
        for item in self.items_tree.get_children():
            self.items_tree.delete(item)

        # إضافة البيانات الجديدة
        items_df = self.db.get_items()
        total_value = 0

        for _, row in items_df.iterrows():
            price = row['item_price']
            total_value += price
            self.items_tree.insert('', 'end', values=(
                row['item_code'], row['item_name'],
                f"{price:,.2f}", row['item_description']
            ))

        # تحديث الإحصائيات
        avg_price = total_value/len(items_df) if len(items_df) > 0 else 0
        self.items_stats_label.config(text=f"📦 إجمالي عدد السلع: {len(items_df)} | متوسط السعر: {avg_price:,.2f}")
        self.update_status(f"تم تحديث {len(items_df)} سلعة")

    def search_items(self, search_term):
        """البحث في السلع"""
        # مسح البيانات الحالية
        for item in self.items_tree.get_children():
            self.items_tree.delete(item)

        # البحث وعرض النتائج
        items_df = self.db.get_items()
        if search_term:
            mask = (items_df['item_name'].str.contains(search_term, case=False, na=False) |
                    items_df['item_code'].str.contains(search_term, case=False, na=False) |
                    items_df['item_description'].str.contains(search_term, case=False, na=False))
            items_df = items_df[mask]

        total_value = 0
        for _, row in items_df.iterrows():
            price = row['item_price']
            total_value += price
            self.items_tree.insert('', 'end', values=(
                row['item_code'], row['item_name'],
                f"{price:,.2f}", row['item_description']
            ))

        self.items_stats_label.config(text=f"📦 نتائج البحث: {len(items_df)} سلعة")
        self.update_status(f"تم العثور على {len(items_df)} سلعة")

    def on_item_select(self, event):
        """عند تحديد سلعة"""
        selection = self.items_tree.selection()
        if selection:
            item = self.items_tree.item(selection[0])
            self.selected_item = item['values'][0]  # كود السلعة
            self.update_status(f"تم تحديد السلعة: {self.selected_item}")

    def add_item_dialog(self):
        """نافذة إضافة سلعة جديدة"""
        dialog = tk.Toplevel(self.root)
        dialog.title("إضافة سلعة جديدة")
        dialog.geometry("600x500")
        dialog.configure(bg='white')
        dialog.transient(self.root)
        dialog.grab_set()

        # عنوان النافذة
        title = tk.Label(dialog, text="➕ إضافة سلعة جديدة",
                        font=('Arial', 16, 'bold'), bg='white', fg='#2c3e50')
        title.pack(pady=20)

        # إطار الحقول
        fields_frame = tk.Frame(dialog, bg='white')
        fields_frame.pack(fill='both', expand=True, padx=30, pady=20)

        # حقول الإدخال
        tk.Label(fields_frame, text="كود السلعة:", font=('Arial', 12, 'bold'), bg='white').grid(row=0, column=0, sticky='w', pady=10)
        code_entry = tk.Entry(fields_frame, font=('Arial', 12), width=20)
        code_entry.grid(row=0, column=1, padx=10, pady=10, sticky='ew')

        tk.Label(fields_frame, text="اسم السلعة:", font=('Arial', 12, 'bold'), bg='white').grid(row=1, column=0, sticky='w', pady=10)
        name_entry = tk.Entry(fields_frame, font=('Arial', 12), width=30)
        name_entry.grid(row=1, column=1, padx=10, pady=10, sticky='ew')

        tk.Label(fields_frame, text="سعر السلعة:", font=('Arial', 12, 'bold'), bg='white').grid(row=2, column=0, sticky='w', pady=10)
        price_entry = tk.Entry(fields_frame, font=('Arial', 12), width=20)
        price_entry.insert(0, "0.0")
        price_entry.grid(row=2, column=1, padx=10, pady=10, sticky='ew')

        tk.Label(fields_frame, text="وصف السلعة:", font=('Arial', 12, 'bold'), bg='white').grid(row=3, column=0, sticky='nw', pady=10)
        desc_text = tk.Text(fields_frame, font=('Arial', 12), width=40, height=5)
        desc_text.grid(row=3, column=1, padx=10, pady=10, sticky='ew')

        fields_frame.grid_columnconfigure(1, weight=1)

        # أزرار العمليات
        buttons_frame = tk.Frame(dialog, bg='white')
        buttons_frame.pack(fill='x', padx=30, pady=20)

        def save_item():
            code = code_entry.get().strip()
            name = name_entry.get().strip()
            desc = desc_text.get("1.0", tk.END).strip()
            try:
                price = float(price_entry.get().strip())
            except ValueError:
                messagebox.showerror("خطأ", "السعر يجب أن يكون رقماً")
                return

            if not code or not name:
                messagebox.showerror("خطأ", "يرجى ملء الحقول المطلوبة")
                return

            if self.db.add_item(code, name, price, desc):
                messagebox.showinfo("نجح", "تم إضافة السلعة بنجاح")
                dialog.destroy()
                self.refresh_items()
            else:
                messagebox.showerror("خطأ", "كود السلعة موجود مسبقاً")

        save_btn = tk.Button(buttons_frame, text="💾 حفظ", command=save_item,
                            bg='#27ae60', fg='white', font=('Arial', 12, 'bold'),
                            relief='flat', padx=20, pady=8, cursor='hand2')
        save_btn.pack(side='left', padx=5)

        cancel_btn = tk.Button(buttons_frame, text="❌ إلغاء", command=dialog.destroy,
                              bg='#e74c3c', fg='white', font=('Arial', 12, 'bold'),
                              relief='flat', padx=20, pady=8, cursor='hand2')
        cancel_btn.pack(side='right', padx=5)

        # التركيز على أول حقل
        code_entry.focus()

    def edit_item_dialog(self, event=None):
        """نافذة تعديل سلعة"""
        if not self.selected_item:
            messagebox.showwarning("تحذير", "يرجى تحديد سلعة للتعديل")
            return

        # الحصول على بيانات السلعة
        item_data = self.db.get_item_by_code(self.selected_item)
        if not item_data:
            messagebox.showerror("خطأ", "لم يتم العثور على السلعة")
            return

        dialog = tk.Toplevel(self.root)
        dialog.title("تعديل السلعة")
        dialog.geometry("600x500")
        dialog.configure(bg='white')
        dialog.transient(self.root)
        dialog.grab_set()

        # عنوان النافذة
        title = tk.Label(dialog, text="✏️ تعديل السلعة",
                        font=('Arial', 16, 'bold'), bg='white', fg='#2c3e50')
        title.pack(pady=20)

        # إطار الحقول
        fields_frame = tk.Frame(dialog, bg='white')
        fields_frame.pack(fill='both', expand=True, padx=30, pady=20)

        # حقول الإدخال مع البيانات الحالية
        tk.Label(fields_frame, text="كود السلعة:", font=('Arial', 12, 'bold'), bg='white').grid(row=0, column=0, sticky='w', pady=10)
        code_entry = tk.Entry(fields_frame, font=('Arial', 12), width=20)
        code_entry.insert(0, item_data[0])  # item_code
        code_entry.grid(row=0, column=1, padx=10, pady=10, sticky='ew')

        tk.Label(fields_frame, text="اسم السلعة:", font=('Arial', 12, 'bold'), bg='white').grid(row=1, column=0, sticky='w', pady=10)
        name_entry = tk.Entry(fields_frame, font=('Arial', 12), width=30)
        name_entry.insert(0, item_data[1])  # item_name
        name_entry.grid(row=1, column=1, padx=10, pady=10, sticky='ew')

        tk.Label(fields_frame, text="سعر السلعة:", font=('Arial', 12, 'bold'), bg='white').grid(row=2, column=0, sticky='w', pady=10)
        price_entry = tk.Entry(fields_frame, font=('Arial', 12), width=20)
        price_entry.insert(0, str(item_data[2]))  # item_price
        price_entry.grid(row=2, column=1, padx=10, pady=10, sticky='ew')

        tk.Label(fields_frame, text="وصف السلعة:", font=('Arial', 12, 'bold'), bg='white').grid(row=3, column=0, sticky='nw', pady=10)
        desc_text = tk.Text(fields_frame, font=('Arial', 12), width=40, height=5)
        desc_text.insert("1.0", item_data[3])  # item_description
        desc_text.grid(row=3, column=1, padx=10, pady=10, sticky='ew')

        fields_frame.grid_columnconfigure(1, weight=1)

        # أزرار العمليات
        buttons_frame = tk.Frame(dialog, bg='white')
        buttons_frame.pack(fill='x', padx=30, pady=20)

        def update_item():
            new_code = code_entry.get().strip()
            name = name_entry.get().strip()
            desc = desc_text.get("1.0", tk.END).strip()
            try:
                price = float(price_entry.get().strip())
            except ValueError:
                messagebox.showerror("خطأ", "السعر يجب أن يكون رقماً")
                return

            if not new_code or not name:
                messagebox.showerror("خطأ", "يرجى ملء الحقول المطلوبة")
                return

            if self.db.update_item(self.selected_item, new_code, name, price, desc):
                messagebox.showinfo("نجح", "تم تحديث السلعة بنجاح")
                dialog.destroy()
                self.refresh_items()
                self.selected_item = new_code
            else:
                messagebox.showerror("خطأ", "فشل في تحديث السلعة")

        update_btn = tk.Button(buttons_frame, text="💾 تحديث", command=update_item,
                              bg='#f39c12', fg='white', font=('Arial', 12, 'bold'),
                              relief='flat', padx=20, pady=8, cursor='hand2')
        update_btn.pack(side='left', padx=5)

        cancel_btn = tk.Button(buttons_frame, text="❌ إلغاء", command=dialog.destroy,
                              bg='#e74c3c', fg='white', font=('Arial', 12, 'bold'),
                              relief='flat', padx=20, pady=8, cursor='hand2')
        cancel_btn.pack(side='right', padx=5)

    def delete_item(self):
        """حذف سلعة"""
        if not self.selected_item:
            messagebox.showwarning("تحذير", "يرجى تحديد سلعة للحذف")
            return

        # تأكيد الحذف
        result = messagebox.askyesno("تأكيد الحذف",
                                   f"هل أنت متأكد من حذف السلعة '{self.selected_item}'؟\n"
                                   "هذا الإجراء لا يمكن التراجع عنه!")

        if result:
            success, message = self.db.delete_item(self.selected_item)
            if success:
                messagebox.showinfo("نجح", message)
                self.refresh_items()
                self.selected_item = None
            else:
                messagebox.showerror("خطأ", message)

    def show_journal_entries_pro(self):
        """عرض إدارة القيود الاحترافية (مؤقت)"""
        self.clear_content()
        self.update_status("عرض إدارة القيود")

        title = tk.Label(self.content_frame, text="📝 إدارة القيود - قريباً",
                        font=('Arial', 18, 'bold'), bg='white', fg='#2c3e50')
        title.pack(pady=50)

        info = tk.Label(self.content_frame, text="هذه الصفحة قيد التطوير...",
                       font=('Arial', 14), bg='white', fg='#7f8c8d')
        info.pack(pady=20)

    def show_statements_pro(self):
        """عرض كشوف الحسابات الاحترافية (مؤقت)"""
        self.clear_content()
        self.update_status("عرض كشوف الحسابات")

        title = tk.Label(self.content_frame, text="📊 كشوف الحسابات - قريباً",
                        font=('Arial', 18, 'bold'), bg='white', fg='#2c3e50')
        title.pack(pady=50)

        info = tk.Label(self.content_frame, text="هذه الصفحة قيد التطوير...",
                       font=('Arial', 14), bg='white', fg='#7f8c8d')
        info.pack(pady=20)

    def show_reports_pro(self):
        """عرض التقارير الاحترافية (مؤقت)"""
        self.clear_content()
        self.update_status("عرض التقارير")

        title = tk.Label(self.content_frame, text="📈 التقارير - قريباً",
                        font=('Arial', 18, 'bold'), bg='white', fg='#2c3e50')
        title.pack(pady=50)

        info = tk.Label(self.content_frame, text="هذه الصفحة قيد التطوير...",
                       font=('Arial', 14), bg='white', fg='#7f8c8d')
        info.pack(pady=20)

    def show_export_pro(self):
        """عرض تصدير البيانات الاحترافي (مؤقت)"""
        self.clear_content()
        self.update_status("عرض تصدير البيانات")

        title = tk.Label(self.content_frame, text="📤 تصدير البيانات - قريباً",
                        font=('Arial', 18, 'bold'), bg='white', fg='#2c3e50')
        title.pack(pady=50)

        info = tk.Label(self.content_frame, text="هذه الصفحة قيد التطوير...",
                       font=('Arial', 14), bg='white', fg='#7f8c8d')
        info.pack(pady=20)

    def show_settings(self):
        """عرض الإعدادات"""
        self.clear_content()
        self.update_status("عرض الإعدادات")

        title = tk.Label(self.content_frame, text="⚙️ إعدادات النظام",
                        font=('Arial', 18, 'bold'), bg='white', fg='#2c3e50')
        title.pack(pady=30)

        # إطار الإعدادات
        settings_frame = tk.LabelFrame(self.content_frame, text="الإعدادات العامة",
                                     font=('Arial', 12, 'bold'), bg='white')
        settings_frame.pack(fill='both', expand=True, padx=30, pady=20)

        # معلومات النظام
        info_frame = tk.Frame(settings_frame, bg='white')
        info_frame.pack(fill='x', padx=20, pady=20)

        tk.Label(info_frame, text="معلومات النظام:", font=('Arial', 14, 'bold'), bg='white').pack(anchor='w')
        tk.Label(info_frame, text="الاسم: نظام المحاسبة البسيط", font=('Arial', 11), bg='white').pack(anchor='w', pady=2)
        tk.Label(info_frame, text="الإصدار: 2.0 Pro", font=('Arial', 11), bg='white').pack(anchor='w', pady=2)
        tk.Label(info_frame, text="المطور: Augment Agent", font=('Arial', 11), bg='white').pack(anchor='w', pady=2)

        # إحصائيات قاعدة البيانات
        stats_frame = tk.Frame(settings_frame, bg='white')
        stats_frame.pack(fill='x', padx=20, pady=20)

        tk.Label(stats_frame, text="إحصائيات قاعدة البيانات:", font=('Arial', 14, 'bold'), bg='white').pack(anchor='w')

        accounts_count = len(self.db.get_accounts())
        cash_boxes_count = len(self.db.get_cash_boxes())
        items_count = len(self.db.get_items())

        tk.Label(stats_frame, text=f"عدد الحسابات: {accounts_count}", font=('Arial', 11), bg='white').pack(anchor='w', pady=2)
        tk.Label(stats_frame, text=f"عدد الصناديق: {cash_boxes_count}", font=('Arial', 11), bg='white').pack(anchor='w', pady=2)
        tk.Label(stats_frame, text=f"عدد السلع: {items_count}", font=('Arial', 11), bg='white').pack(anchor='w', pady=2)

        # أزرار الصيانة
        maintenance_frame = tk.Frame(settings_frame, bg='white')
        maintenance_frame.pack(fill='x', padx=20, pady=20)

        tk.Label(maintenance_frame, text="صيانة النظام:", font=('Arial', 14, 'bold'), bg='white').pack(anchor='w')

        backup_btn = tk.Button(maintenance_frame, text="🗄️ نسخة احتياطية",
                              bg='#3498db', fg='white', font=('Arial', 11, 'bold'),
                              relief='flat', padx=20, pady=8, cursor='hand2')
        backup_btn.pack(side='left', padx=5, pady=10)

        optimize_btn = tk.Button(maintenance_frame, text="⚡ تحسين قاعدة البيانات",
                               bg='#27ae60', fg='white', font=('Arial', 11, 'bold'),
                               relief='flat', padx=20, pady=8, cursor='hand2')
        optimize_btn.pack(side='left', padx=5, pady=10)

def main():
    """تشغيل التطبيق الاحترافي"""
    root = tk.Tk()
    app = AccountingAppPro(root)

    # تشغيل التطبيق
    try:
        root.mainloop()
    except KeyboardInterrupt:
        print("تم إغلاق التطبيق")
    except Exception as e:
        print(f"خطأ في التطبيق: {e}")

if __name__ == "__main__":
    main()
