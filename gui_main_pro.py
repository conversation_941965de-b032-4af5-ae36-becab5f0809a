#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام المحاسبة البسيط - واجهة رسومية احترافية
الإصدار المحدث مع خصائص التعديل والحذف
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import pandas as pd
from datetime import datetime, date
from database import AccountingDatabase
from utils_gui import *
import os
import sys

class AccountingAppPro:
    def __init__(self, root):
        self.root = root
        self.root.title("نظام المحاسبة البسيط - الإصدار الاحترافي")
        self.root.geometry("1400x900")
        self.root.configure(bg='#f8f9fa')
        
        # تهيئة قاعدة البيانات
        try:
            self.db = AccountingDatabase()
            print("✅ تم تهيئة قاعدة البيانات بنجاح")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تهيئة قاعدة البيانات: {e}")
            sys.exit(1)
        
        # متغيرات للتحديد
        self.selected_account = None
        self.selected_cash_box = None
        self.selected_item = None
        self.selected_entry = None
        
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم الاحترافية"""
        # شريط العنوان
        self.create_header()
        
        # الإطار الرئيسي
        main_frame = tk.Frame(self.root, bg='#f8f9fa')
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # القائمة الجانبية
        self.create_sidebar(main_frame)
        
        # منطقة المحتوى الرئيسي
        self.content_frame = tk.Frame(main_frame, bg='white', relief='raised', bd=1)
        self.content_frame.pack(side='right', fill='both', expand=True, padx=(10, 0))
        
        # شريط الحالة
        self.create_status_bar()
        
        # عرض لوحة التحكم افتراضياً
        self.show_dashboard()
        
    def create_header(self):
        """إنشاء شريط العنوان الاحترافي"""
        header_frame = tk.Frame(self.root, bg='#2c3e50', height=100)
        header_frame.pack(fill='x', padx=10, pady=(10, 5))
        header_frame.pack_propagate(False)

        # العنوان الرئيسي
        title_label = tk.Label(header_frame, text="💰 نظام المحاسبة البسيط - الإصدار الاحترافي",
                              font=('Arial', 18, 'bold'), fg='white', bg='#2c3e50')
        title_label.pack(side='left', padx=20, pady=(15, 5))

        # معلومات المصمم
        designer_frame = tk.Frame(header_frame, bg='#2c3e50')
        designer_frame.pack(side='left', padx=(0, 20), pady=(35, 5))

        designer_label = tk.Label(designer_frame, text="صُمم من قِبل: Hazim G. Daway",
                                 font=('Arial', 9), fg='#ecf0f1', bg='#2c3e50')
        designer_label.pack()

        # معلومات النظام
        info_frame = tk.Frame(header_frame, bg='#2c3e50')
        info_frame.pack(side='right', padx=20, pady=(15, 5))

        date_label = tk.Label(info_frame, text=f"التاريخ: {date.today().strftime('%Y-%m-%d')}",
                             font=('Arial', 10), fg='white', bg='#2c3e50')
        date_label.pack()

        version_label = tk.Label(info_frame, text="الإصدار: 4.0 Complete",
                               font=('Arial', 10), fg='white', bg='#2c3e50')
        version_label.pack()
        
    def create_sidebar(self, parent):
        """إنشاء القائمة الجانبية الاحترافية"""
        sidebar_frame = tk.Frame(parent, bg='#34495e', width=280)
        sidebar_frame.pack(side='left', fill='y')
        sidebar_frame.pack_propagate(False)
        
        # عنوان القائمة
        menu_title = tk.Label(sidebar_frame, text="القائمة الرئيسية", 
                             font=('Arial', 14, 'bold'), fg='white', bg='#34495e')
        menu_title.pack(pady=20)
        
        # أزرار القائمة المحدثة
        menu_buttons = [
            ("📊 لوحة التحكم", self.show_dashboard, "#3498db"),
            ("📋 إدارة الحسابات", self.show_accounts_pro, "#27ae60"),
            ("💰 إدارة الصناديق", self.show_cash_boxes_pro, "#f39c12"),
            ("📦 إدارة السلع", self.show_items_pro, "#e74c3c"),
            ("📝 إدارة القيود", self.show_journal_entries_pro, "#9b59b6"),
            ("📊 كشوف الحسابات", self.show_statements_pro, "#1abc9c"),
            ("📈 التقارير", self.show_reports_pro, "#34495e"),
            ("📤 تصدير البيانات", self.show_export_pro, "#95a5a6"),
            ("⚙️ الإعدادات", self.show_settings, "#7f8c8d")
        ]
        
        for text, command, color in menu_buttons:
            btn = tk.Button(sidebar_frame, text=text, command=command,
                           font=('Arial', 11, 'bold'), bg=color, fg='white',
                           relief='flat', pady=12, width=28, cursor='hand2',
                           activebackground=self.darken_color(color))
            btn.pack(fill='x', padx=15, pady=3)
            
            # تأثير hover
            btn.bind("<Enter>", lambda e, b=btn, c=color: b.config(bg=self.darken_color(c)))
            btn.bind("<Leave>", lambda e, b=btn, c=color: b.config(bg=c))
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_bar = tk.Label(self.root, text="جاهز", relief=tk.SUNKEN, 
                                  anchor=tk.W, bg='#ecf0f1', font=('Arial', 9))
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=(0, 10))
        
    def update_status(self, message):
        """تحديث شريط الحالة"""
        self.status_bar.config(text=f"{datetime.now().strftime('%H:%M:%S')} - {message}")
        self.root.update()
        
    def darken_color(self, color):
        """تغميق اللون للتأثيرات"""
        color_map = {
            "#3498db": "#2980b9",
            "#27ae60": "#229954",
            "#f39c12": "#e67e22",
            "#e74c3c": "#c0392b",
            "#9b59b6": "#8e44ad",
            "#1abc9c": "#16a085",
            "#34495e": "#2c3e50",
            "#95a5a6": "#7f8c8d",
            "#7f8c8d": "#6c7b7d"
        }
        return color_map.get(color, color)
        
    def clear_content(self):
        """مسح المحتوى الحالي"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()
    
    def create_action_buttons(self, parent, add_func, edit_func, delete_func):
        """إنشاء أزرار العمليات الموحدة"""
        buttons_frame = tk.Frame(parent, bg='white')
        buttons_frame.pack(fill='x', padx=20, pady=10)
        
        # زر الإضافة
        add_btn = tk.Button(buttons_frame, text="➕ إضافة جديد", command=add_func,
                           bg='#27ae60', fg='white', font=('Arial', 11, 'bold'),
                           relief='flat', padx=20, pady=8, cursor='hand2')
        add_btn.pack(side='left', padx=5)
        
        # زر التعديل
        edit_btn = tk.Button(buttons_frame, text="✏️ تعديل", command=edit_func,
                            bg='#f39c12', fg='white', font=('Arial', 11, 'bold'),
                            relief='flat', padx=20, pady=8, cursor='hand2')
        edit_btn.pack(side='left', padx=5)
        
        # زر الحذف
        delete_btn = tk.Button(buttons_frame, text="🗑️ حذف", command=delete_func,
                              bg='#e74c3c', fg='white', font=('Arial', 11, 'bold'),
                              relief='flat', padx=20, pady=8, cursor='hand2')
        delete_btn.pack(side='left', padx=5)
        
        # زر التحديث
        refresh_btn = tk.Button(buttons_frame, text="🔄 تحديث", command=lambda: self.update_status("تم تحديث البيانات"),
                               bg='#3498db', fg='white', font=('Arial', 11, 'bold'),
                               relief='flat', padx=20, pady=8, cursor='hand2')
        refresh_btn.pack(side='right', padx=5)
        
        return add_btn, edit_btn, delete_btn, refresh_btn
    
    def create_search_bar(self, parent, search_func):
        """إنشاء شريط البحث"""
        search_frame = tk.Frame(parent, bg='white')
        search_frame.pack(fill='x', padx=20, pady=10)
        
        tk.Label(search_frame, text="🔍 البحث:", font=('Arial', 11, 'bold'), bg='white').pack(side='left', padx=5)
        
        search_var = tk.StringVar()
        search_entry = tk.Entry(search_frame, textvariable=search_var, font=('Arial', 11), width=30)
        search_entry.pack(side='left', padx=5)
        
        search_btn = tk.Button(search_frame, text="بحث", command=lambda: search_func(search_var.get()),
                              bg='#3498db', fg='white', font=('Arial', 10, 'bold'),
                              relief='flat', padx=15, pady=5, cursor='hand2')
        search_btn.pack(side='left', padx=5)
        
        # البحث التلقائي عند الكتابة
        search_var.trace('w', lambda *args: search_func(search_var.get()))
        
        return search_var, search_entry
    
    def show_dashboard(self):
        """عرض لوحة التحكم المحدثة"""
        self.clear_content()
        self.update_status("عرض لوحة التحكم")
        
        # عنوان الصفحة
        title = tk.Label(self.content_frame, text="📊 لوحة التحكم", 
                        font=('Arial', 20, 'bold'), bg='white', fg='#2c3e50')
        title.pack(pady=30)
        
        # إطار الإحصائيات المحدث
        stats_frame = tk.Frame(self.content_frame, bg='white')
        stats_frame.pack(fill='x', padx=30, pady=20)
        
        # الحصول على الإحصائيات
        accounts_count = len(self.db.get_accounts())
        cash_boxes = self.db.get_cash_boxes()
        total_cash = cash_boxes['current_balance'].sum()
        items_count = len(self.db.get_items())
        
        # عدد القيود اليوم
        today = date.today().strftime('%Y-%m-%d')
        conn = self.db.get_connection()
        today_entries = pd.read_sql_query(
            "SELECT COUNT(*) as count FROM journal_entries WHERE entry_date = ?", 
            conn, params=[today]
        )
        conn.close()
        
        # عرض الإحصائيات بتصميم احترافي
        stats = [
            ("عدد الحسابات", accounts_count, "#3498db", "📋"),
            ("إجمالي النقدية", f"{total_cash:,.2f}", "#27ae60", "💰"),
            ("عدد السلع", items_count, "#e74c3c", "📦"),
            ("قيود اليوم", today_entries['count'].iloc[0], "#9b59b6", "📝")
        ]
        
        for i, (label, value, color, icon) in enumerate(stats):
            stat_frame = tk.Frame(stats_frame, bg=color, relief='raised', bd=3)
            stat_frame.grid(row=0, column=i, padx=15, pady=10, sticky='ew', ipadx=20, ipady=20)
            stats_frame.grid_columnconfigure(i, weight=1)
            
            # الأيقونة
            icon_label = tk.Label(stat_frame, text=icon, font=('Arial', 24), 
                                 fg='white', bg=color)
            icon_label.pack(pady=(10, 5))
            
            # القيمة
            value_label = tk.Label(stat_frame, text=str(value), font=('Arial', 18, 'bold'), 
                                  fg='white', bg=color)
            value_label.pack()
            
            # التسمية
            label_label = tk.Label(stat_frame, text=label, font=('Arial', 12), 
                                  fg='white', bg=color)
            label_label.pack(pady=(5, 10))
        
        # آخر القيود مع تصميم محدث
        recent_frame = tk.LabelFrame(self.content_frame, text="📝 آخر القيود المحاسبية", 
                                   font=('Arial', 14, 'bold'), bg='white', fg='#2c3e50')
        recent_frame.pack(fill='both', expand=True, padx=30, pady=20)
        
        # جدول آخر القيود مع تحسينات
        columns = ('رقم القيد', 'التاريخ', 'الوصف', 'المبلغ')
        tree = ttk.Treeview(recent_frame, columns=columns, show='headings', height=12)
        
        # تنسيق الأعمدة
        tree.heading('رقم القيد', text='رقم القيد')
        tree.heading('التاريخ', text='التاريخ')
        tree.heading('الوصف', text='الوصف')
        tree.heading('المبلغ', text='إجمالي المبلغ')
        
        tree.column('رقم القيد', width=100, anchor='center')
        tree.column('التاريخ', width=120, anchor='center')
        tree.column('الوصف', width=300, anchor='w')
        tree.column('المبلغ', width=150, anchor='e')
        
        # الحصول على آخر القيود مع المبالغ
        conn = self.db.get_connection()
        recent_entries = pd.read_sql_query(
            """SELECT je.entry_id, je.entry_date, je.description,
                      SUM(ed.debit_amount) as total_amount
               FROM journal_entries je
               LEFT JOIN entry_details ed ON je.entry_id = ed.entry_id
               GROUP BY je.entry_id, je.entry_date, je.description
               ORDER BY je.entry_id DESC 
               LIMIT 10""", 
            conn
        )
        conn.close()
        
        # إضافة البيانات مع تنسيق
        for _, row in recent_entries.iterrows():
            amount_formatted = f"{row['total_amount']:,.2f}" if pd.notna(row['total_amount']) else "0.00"
            tree.insert('', 'end', values=(
                row['entry_id'], 
                row['entry_date'], 
                row['description'][:50] + "..." if len(row['description']) > 50 else row['description'],
                amount_formatted
            ))
        
        tree.pack(fill='both', expand=True, padx=15, pady=15)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(recent_frame, orient='vertical', command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side='right', fill='y')
        
        # رسالة ترحيب
        if recent_entries.empty:
            welcome_label = tk.Label(recent_frame, text="مرحباً بك في نظام المحاسبة البسيط!\nابدأ بإضافة الحسابات والقيود المحاسبية",
                                   font=('Arial', 12), bg='white', fg='#7f8c8d')
            welcome_label.pack(pady=30)

            # معلومات المصمم في لوحة التحكم
            designer_info = tk.Label(recent_frame, text="صُمم وطُور بواسطة: Hazim G. Daway",
                                   font=('Arial', 10, 'italic'), bg='white', fg='#95a5a6')
            designer_info.pack(pady=10)

    def show_accounts_pro(self):
        """عرض إدارة الحسابات الاحترافية"""
        self.clear_content()
        self.update_status("عرض إدارة الحسابات")

        # عنوان الصفحة
        title = tk.Label(self.content_frame, text="📋 إدارة الحسابات",
                        font=('Arial', 18, 'bold'), bg='white', fg='#2c3e50')
        title.pack(pady=20)

        # شريط البحث
        search_var, search_entry = self.create_search_bar(self.content_frame, self.search_accounts)

        # أزرار العمليات
        add_btn, edit_btn, delete_btn, refresh_btn = self.create_action_buttons(
            self.content_frame, self.add_account_dialog, self.edit_account_dialog, self.delete_account
        )

        # جدول الحسابات
        accounts_frame = tk.LabelFrame(self.content_frame, text="قائمة الحسابات",
                                     font=('Arial', 12, 'bold'), bg='white')
        accounts_frame.pack(fill='both', expand=True, padx=20, pady=10)

        columns = ('كود الحساب', 'اسم الحساب', 'نوع الحساب', 'تاريخ الإنشاء')
        self.accounts_tree = ttk.Treeview(accounts_frame, columns=columns, show='headings')

        # تنسيق الأعمدة
        for col in columns:
            self.accounts_tree.heading(col, text=col)
            self.accounts_tree.column(col, width=200, anchor='center')

        # ربط الأحداث
        self.accounts_tree.bind('<ButtonRelease-1>', self.on_account_select)
        self.accounts_tree.bind('<Double-1>', self.edit_account_dialog)

        self.accounts_tree.pack(fill='both', expand=True, padx=10, pady=10)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(accounts_frame, orient='vertical', command=self.accounts_tree.yview)
        self.accounts_tree.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side='right', fill='y')

        # تحديث البيانات
        self.refresh_accounts()

        # ربط زر التحديث
        refresh_btn.config(command=self.refresh_accounts)

    def refresh_accounts(self):
        """تحديث قائمة الحسابات"""
        # مسح البيانات الحالية
        for item in self.accounts_tree.get_children():
            self.accounts_tree.delete(item)

        # إضافة البيانات الجديدة
        accounts_df = self.db.get_accounts()
        for _, row in accounts_df.iterrows():
            self.accounts_tree.insert('', 'end', values=(
                row['account_code'], row['account_name'],
                row['account_type'], row['created_date'][:10]
            ))

        self.update_status(f"تم تحديث {len(accounts_df)} حساب")

    def search_accounts(self, search_term):
        """البحث في الحسابات"""
        # مسح البيانات الحالية
        for item in self.accounts_tree.get_children():
            self.accounts_tree.delete(item)

        # البحث وعرض النتائج
        accounts_df = search_accounts(self.db, search_term)
        for _, row in accounts_df.iterrows():
            self.accounts_tree.insert('', 'end', values=(
                row['account_code'], row['account_name'],
                row['account_type'], row['created_date'][:10]
            ))

        self.update_status(f"تم العثور على {len(accounts_df)} حساب")

    def on_account_select(self, event):
        """عند تحديد حساب"""
        selection = self.accounts_tree.selection()
        if selection:
            item = self.accounts_tree.item(selection[0])
            self.selected_account = item['values'][0]  # كود الحساب
            self.update_status(f"تم تحديد الحساب: {self.selected_account}")

    def add_account_dialog(self):
        """نافذة إضافة حساب جديد"""
        dialog = tk.Toplevel(self.root)
        dialog.title("إضافة حساب جديد")
        dialog.geometry("500x400")
        dialog.configure(bg='white')
        dialog.transient(self.root)
        dialog.grab_set()

        # عنوان النافذة
        title = tk.Label(dialog, text="➕ إضافة حساب جديد",
                        font=('Arial', 16, 'bold'), bg='white', fg='#2c3e50')
        title.pack(pady=20)

        # إطار الحقول
        fields_frame = tk.Frame(dialog, bg='white')
        fields_frame.pack(fill='both', expand=True, padx=30, pady=20)

        # حقول الإدخال
        tk.Label(fields_frame, text="كود الحساب:", font=('Arial', 12, 'bold'), bg='white').grid(row=0, column=0, sticky='w', pady=10)
        code_entry = tk.Entry(fields_frame, font=('Arial', 12), width=20)
        code_entry.grid(row=0, column=1, padx=10, pady=10, sticky='ew')

        tk.Label(fields_frame, text="اسم الحساب:", font=('Arial', 12, 'bold'), bg='white').grid(row=1, column=0, sticky='w', pady=10)
        name_entry = tk.Entry(fields_frame, font=('Arial', 12), width=30)
        name_entry.grid(row=1, column=1, padx=10, pady=10, sticky='ew')

        tk.Label(fields_frame, text="نوع الحساب:", font=('Arial', 12, 'bold'), bg='white').grid(row=2, column=0, sticky='w', pady=10)
        type_combo = ttk.Combobox(fields_frame, values=get_account_types(), font=('Arial', 12), width=18)
        type_combo.grid(row=2, column=1, padx=10, pady=10, sticky='ew')

        fields_frame.grid_columnconfigure(1, weight=1)

        # أزرار العمليات
        buttons_frame = tk.Frame(dialog, bg='white')
        buttons_frame.pack(fill='x', padx=30, pady=20)

        def save_account():
            code = code_entry.get().strip()
            name = name_entry.get().strip()
            acc_type = type_combo.get().strip()

            if not code or not name or not acc_type:
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول")
                return

            if self.db.add_account(code, name, acc_type):
                messagebox.showinfo("نجح", "تم إضافة الحساب بنجاح")
                dialog.destroy()
                self.refresh_accounts()
            else:
                messagebox.showerror("خطأ", "كود الحساب موجود مسبقاً")

        save_btn = tk.Button(buttons_frame, text="💾 حفظ", command=save_account,
                            bg='#27ae60', fg='white', font=('Arial', 12, 'bold'),
                            relief='flat', padx=20, pady=8, cursor='hand2')
        save_btn.pack(side='left', padx=5)

        cancel_btn = tk.Button(buttons_frame, text="❌ إلغاء", command=dialog.destroy,
                              bg='#e74c3c', fg='white', font=('Arial', 12, 'bold'),
                              relief='flat', padx=20, pady=8, cursor='hand2')
        cancel_btn.pack(side='right', padx=5)

        # التركيز على أول حقل
        code_entry.focus()

    def edit_account_dialog(self, event=None):
        """نافذة تعديل حساب"""
        if not self.selected_account:
            messagebox.showwarning("تحذير", "يرجى تحديد حساب للتعديل")
            return

        # الحصول على بيانات الحساب
        account_data = self.db.get_account_by_code(self.selected_account)
        if not account_data:
            messagebox.showerror("خطأ", "لم يتم العثور على الحساب")
            return

        dialog = tk.Toplevel(self.root)
        dialog.title("تعديل الحساب")
        dialog.geometry("500x400")
        dialog.configure(bg='white')
        dialog.transient(self.root)
        dialog.grab_set()

        # عنوان النافذة
        title = tk.Label(dialog, text="✏️ تعديل الحساب",
                        font=('Arial', 16, 'bold'), bg='white', fg='#2c3e50')
        title.pack(pady=20)

        # إطار الحقول
        fields_frame = tk.Frame(dialog, bg='white')
        fields_frame.pack(fill='both', expand=True, padx=30, pady=20)

        # حقول الإدخال مع البيانات الحالية
        tk.Label(fields_frame, text="كود الحساب:", font=('Arial', 12, 'bold'), bg='white').grid(row=0, column=0, sticky='w', pady=10)
        code_entry = tk.Entry(fields_frame, font=('Arial', 12), width=20)
        code_entry.insert(0, account_data[0])  # account_code
        code_entry.grid(row=0, column=1, padx=10, pady=10, sticky='ew')

        tk.Label(fields_frame, text="اسم الحساب:", font=('Arial', 12, 'bold'), bg='white').grid(row=1, column=0, sticky='w', pady=10)
        name_entry = tk.Entry(fields_frame, font=('Arial', 12), width=30)
        name_entry.insert(0, account_data[1])  # account_name
        name_entry.grid(row=1, column=1, padx=10, pady=10, sticky='ew')

        tk.Label(fields_frame, text="نوع الحساب:", font=('Arial', 12, 'bold'), bg='white').grid(row=2, column=0, sticky='w', pady=10)
        type_combo = ttk.Combobox(fields_frame, values=get_account_types(), font=('Arial', 12), width=18)
        type_combo.set(account_data[2])  # account_type
        type_combo.grid(row=2, column=1, padx=10, pady=10, sticky='ew')

        fields_frame.grid_columnconfigure(1, weight=1)

        # أزرار العمليات
        buttons_frame = tk.Frame(dialog, bg='white')
        buttons_frame.pack(fill='x', padx=30, pady=20)

        def update_account():
            new_code = code_entry.get().strip()
            name = name_entry.get().strip()
            acc_type = type_combo.get().strip()

            if not new_code or not name or not acc_type:
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول")
                return

            if self.db.update_account(self.selected_account, new_code, name, acc_type):
                messagebox.showinfo("نجح", "تم تحديث الحساب بنجاح")
                dialog.destroy()
                self.refresh_accounts()
                self.selected_account = new_code
            else:
                messagebox.showerror("خطأ", "فشل في تحديث الحساب")

        update_btn = tk.Button(buttons_frame, text="💾 تحديث", command=update_account,
                              bg='#f39c12', fg='white', font=('Arial', 12, 'bold'),
                              relief='flat', padx=20, pady=8, cursor='hand2')
        update_btn.pack(side='left', padx=5)

        cancel_btn = tk.Button(buttons_frame, text="❌ إلغاء", command=dialog.destroy,
                              bg='#e74c3c', fg='white', font=('Arial', 12, 'bold'),
                              relief='flat', padx=20, pady=8, cursor='hand2')
        cancel_btn.pack(side='right', padx=5)

    def delete_account(self):
        """حذف حساب"""
        if not self.selected_account:
            messagebox.showwarning("تحذير", "يرجى تحديد حساب للحذف")
            return

        # تأكيد الحذف
        result = messagebox.askyesno("تأكيد الحذف",
                                   f"هل أنت متأكد من حذف الحساب '{self.selected_account}'؟\n"
                                   "هذا الإجراء لا يمكن التراجع عنه!")

        if result:
            success, message = self.db.delete_account(self.selected_account)
            if success:
                messagebox.showinfo("نجح", message)
                self.refresh_accounts()
                self.selected_account = None
            else:
                messagebox.showerror("خطأ", message)

    # ==================== إدارة الصناديق الاحترافية ====================

    def show_cash_boxes_pro(self):
        """عرض إدارة الصناديق الاحترافية"""
        self.clear_content()
        self.update_status("عرض إدارة الصناديق")

        # عنوان الصفحة
        title = tk.Label(self.content_frame, text="💰 إدارة الصناديق",
                        font=('Arial', 18, 'bold'), bg='white', fg='#2c3e50')
        title.pack(pady=20)

        # شريط البحث
        search_var, search_entry = self.create_search_bar(self.content_frame, self.search_cash_boxes)

        # أزرار العمليات
        add_btn, edit_btn, delete_btn, refresh_btn = self.create_action_buttons(
            self.content_frame, self.add_cash_box_dialog, self.edit_cash_box_dialog, self.delete_cash_box
        )

        # جدول الصناديق
        boxes_frame = tk.LabelFrame(self.content_frame, text="قائمة الصناديق",
                                   font=('Arial', 12, 'bold'), bg='white')
        boxes_frame.pack(fill='both', expand=True, padx=20, pady=10)

        columns = ('كود الصندوق', 'اسم الصندوق', 'الرصيد الحالي', 'تاريخ الإنشاء')
        self.cash_boxes_tree = ttk.Treeview(boxes_frame, columns=columns, show='headings')

        # تنسيق الأعمدة
        for col in columns:
            self.cash_boxes_tree.heading(col, text=col)
            if col == 'الرصيد الحالي':
                self.cash_boxes_tree.column(col, width=150, anchor='e')
            else:
                self.cash_boxes_tree.column(col, width=200, anchor='center')

        # ربط الأحداث
        self.cash_boxes_tree.bind('<ButtonRelease-1>', self.on_cash_box_select)
        self.cash_boxes_tree.bind('<Double-1>', self.edit_cash_box_dialog)

        self.cash_boxes_tree.pack(fill='both', expand=True, padx=10, pady=10)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(boxes_frame, orient='vertical', command=self.cash_boxes_tree.yview)
        self.cash_boxes_tree.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side='right', fill='y')

        # إجمالي الأرصدة
        self.total_cash_label = tk.Label(self.content_frame, text="",
                                        font=('Arial', 14, 'bold'), bg='white', fg='#27ae60')
        self.total_cash_label.pack(pady=10)

        # تحديث البيانات
        self.refresh_cash_boxes()

        # ربط زر التحديث
        refresh_btn.config(command=self.refresh_cash_boxes)

    def refresh_cash_boxes(self):
        """تحديث قائمة الصناديق"""
        # مسح البيانات الحالية
        for item in self.cash_boxes_tree.get_children():
            self.cash_boxes_tree.delete(item)

        # إضافة البيانات الجديدة
        boxes_df = self.db.get_cash_boxes()
        total_balance = 0

        for _, row in boxes_df.iterrows():
            balance = row['current_balance']
            total_balance += balance
            self.cash_boxes_tree.insert('', 'end', values=(
                row['box_code'], row['box_name'],
                f"{balance:,.2f}", row['created_date'][:10]
            ))

        # تحديث الإجمالي
        self.total_cash_label.config(text=f"💰 إجمالي أرصدة الصناديق: {total_balance:,.2f}")
        self.update_status(f"تم تحديث {len(boxes_df)} صندوق")

    def search_cash_boxes(self, search_term):
        """البحث في الصناديق"""
        # مسح البيانات الحالية
        for item in self.cash_boxes_tree.get_children():
            self.cash_boxes_tree.delete(item)

        # البحث وعرض النتائج
        boxes_df = self.db.get_cash_boxes()
        if search_term:
            mask = (boxes_df['box_name'].str.contains(search_term, case=False, na=False) |
                    boxes_df['box_code'].str.contains(search_term, case=False, na=False))
            boxes_df = boxes_df[mask]

        total_balance = 0
        for _, row in boxes_df.iterrows():
            balance = row['current_balance']
            total_balance += balance
            self.cash_boxes_tree.insert('', 'end', values=(
                row['box_code'], row['box_name'],
                f"{balance:,.2f}", row['created_date'][:10]
            ))

        self.total_cash_label.config(text=f"💰 إجمالي أرصدة الصناديق: {total_balance:,.2f}")
        self.update_status(f"تم العثور على {len(boxes_df)} صندوق")

    def on_cash_box_select(self, event):
        """عند تحديد صندوق"""
        selection = self.cash_boxes_tree.selection()
        if selection:
            item = self.cash_boxes_tree.item(selection[0])
            self.selected_cash_box = item['values'][0]  # كود الصندوق
            self.update_status(f"تم تحديد الصندوق: {self.selected_cash_box}")

    def add_cash_box_dialog(self):
        """نافذة إضافة صندوق جديد"""
        dialog = tk.Toplevel(self.root)
        dialog.title("إضافة صندوق جديد")
        dialog.geometry("500x400")
        dialog.configure(bg='white')
        dialog.transient(self.root)
        dialog.grab_set()

        # عنوان النافذة
        title = tk.Label(dialog, text="➕ إضافة صندوق جديد",
                        font=('Arial', 16, 'bold'), bg='white', fg='#2c3e50')
        title.pack(pady=20)

        # إطار الحقول
        fields_frame = tk.Frame(dialog, bg='white')
        fields_frame.pack(fill='both', expand=True, padx=30, pady=20)

        # حقول الإدخال
        tk.Label(fields_frame, text="كود الصندوق:", font=('Arial', 12, 'bold'), bg='white').grid(row=0, column=0, sticky='w', pady=10)
        code_entry = tk.Entry(fields_frame, font=('Arial', 12), width=20)
        code_entry.grid(row=0, column=1, padx=10, pady=10, sticky='ew')

        tk.Label(fields_frame, text="اسم الصندوق:", font=('Arial', 12, 'bold'), bg='white').grid(row=1, column=0, sticky='w', pady=10)
        name_entry = tk.Entry(fields_frame, font=('Arial', 12), width=30)
        name_entry.grid(row=1, column=1, padx=10, pady=10, sticky='ew')

        tk.Label(fields_frame, text="الرصيد الابتدائي:", font=('Arial', 12, 'bold'), bg='white').grid(row=2, column=0, sticky='w', pady=10)
        balance_entry = tk.Entry(fields_frame, font=('Arial', 12), width=20)
        balance_entry.insert(0, "0.0")
        balance_entry.grid(row=2, column=1, padx=10, pady=10, sticky='ew')

        fields_frame.grid_columnconfigure(1, weight=1)

        # أزرار العمليات
        buttons_frame = tk.Frame(dialog, bg='white')
        buttons_frame.pack(fill='x', padx=30, pady=20)

        def save_cash_box():
            code = code_entry.get().strip()
            name = name_entry.get().strip()
            try:
                balance = float(balance_entry.get().strip())
            except ValueError:
                messagebox.showerror("خطأ", "الرصيد الابتدائي يجب أن يكون رقماً")
                return

            if not code or not name:
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول")
                return

            if self.db.add_cash_box(code, name, balance):
                messagebox.showinfo("نجح", "تم إضافة الصندوق بنجاح")
                dialog.destroy()
                self.refresh_cash_boxes()
            else:
                messagebox.showerror("خطأ", "كود الصندوق موجود مسبقاً")

        save_btn = tk.Button(buttons_frame, text="💾 حفظ", command=save_cash_box,
                            bg='#27ae60', fg='white', font=('Arial', 12, 'bold'),
                            relief='flat', padx=20, pady=8, cursor='hand2')
        save_btn.pack(side='left', padx=5)

        cancel_btn = tk.Button(buttons_frame, text="❌ إلغاء", command=dialog.destroy,
                              bg='#e74c3c', fg='white', font=('Arial', 12, 'bold'),
                              relief='flat', padx=20, pady=8, cursor='hand2')
        cancel_btn.pack(side='right', padx=5)

        # التركيز على أول حقل
        code_entry.focus()

    def edit_cash_box_dialog(self, event=None):
        """نافذة تعديل صندوق"""
        if not self.selected_cash_box:
            messagebox.showwarning("تحذير", "يرجى تحديد صندوق للتعديل")
            return

        # الحصول على بيانات الصندوق
        box_data = self.db.get_cash_box_by_code(self.selected_cash_box)
        if not box_data:
            messagebox.showerror("خطأ", "لم يتم العثور على الصندوق")
            return

        dialog = tk.Toplevel(self.root)
        dialog.title("تعديل الصندوق")
        dialog.geometry("500x400")
        dialog.configure(bg='white')
        dialog.transient(self.root)
        dialog.grab_set()

        # عنوان النافذة
        title = tk.Label(dialog, text="✏️ تعديل الصندوق",
                        font=('Arial', 16, 'bold'), bg='white', fg='#2c3e50')
        title.pack(pady=20)

        # إطار الحقول
        fields_frame = tk.Frame(dialog, bg='white')
        fields_frame.pack(fill='both', expand=True, padx=30, pady=20)

        # حقول الإدخال مع البيانات الحالية
        tk.Label(fields_frame, text="كود الصندوق:", font=('Arial', 12, 'bold'), bg='white').grid(row=0, column=0, sticky='w', pady=10)
        code_entry = tk.Entry(fields_frame, font=('Arial', 12), width=20)
        code_entry.insert(0, box_data[0])  # box_code
        code_entry.grid(row=0, column=1, padx=10, pady=10, sticky='ew')

        tk.Label(fields_frame, text="اسم الصندوق:", font=('Arial', 12, 'bold'), bg='white').grid(row=1, column=0, sticky='w', pady=10)
        name_entry = tk.Entry(fields_frame, font=('Arial', 12), width=30)
        name_entry.insert(0, box_data[1])  # box_name
        name_entry.grid(row=1, column=1, padx=10, pady=10, sticky='ew')

        tk.Label(fields_frame, text="الرصيد الحالي:", font=('Arial', 12, 'bold'), bg='white').grid(row=2, column=0, sticky='w', pady=10)
        balance_entry = tk.Entry(fields_frame, font=('Arial', 12), width=20)
        balance_entry.insert(0, str(box_data[2]))  # current_balance
        balance_entry.grid(row=2, column=1, padx=10, pady=10, sticky='ew')

        fields_frame.grid_columnconfigure(1, weight=1)

        # أزرار العمليات
        buttons_frame = tk.Frame(dialog, bg='white')
        buttons_frame.pack(fill='x', padx=30, pady=20)

        def update_cash_box():
            new_code = code_entry.get().strip()
            name = name_entry.get().strip()
            try:
                balance = float(balance_entry.get().strip())
            except ValueError:
                messagebox.showerror("خطأ", "الرصيد يجب أن يكون رقماً")
                return

            if not new_code or not name:
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول")
                return

            if self.db.update_cash_box(self.selected_cash_box, new_code, name, balance):
                messagebox.showinfo("نجح", "تم تحديث الصندوق بنجاح")
                dialog.destroy()
                self.refresh_cash_boxes()
                self.selected_cash_box = new_code
            else:
                messagebox.showerror("خطأ", "فشل في تحديث الصندوق")

        update_btn = tk.Button(buttons_frame, text="💾 تحديث", command=update_cash_box,
                              bg='#f39c12', fg='white', font=('Arial', 12, 'bold'),
                              relief='flat', padx=20, pady=8, cursor='hand2')
        update_btn.pack(side='left', padx=5)

        cancel_btn = tk.Button(buttons_frame, text="❌ إلغاء", command=dialog.destroy,
                              bg='#e74c3c', fg='white', font=('Arial', 12, 'bold'),
                              relief='flat', padx=20, pady=8, cursor='hand2')
        cancel_btn.pack(side='right', padx=5)

    def delete_cash_box(self):
        """حذف صندوق"""
        if not self.selected_cash_box:
            messagebox.showwarning("تحذير", "يرجى تحديد صندوق للحذف")
            return

        # تأكيد الحذف
        result = messagebox.askyesno("تأكيد الحذف",
                                   f"هل أنت متأكد من حذف الصندوق '{self.selected_cash_box}'؟\n"
                                   "هذا الإجراء لا يمكن التراجع عنه!")

        if result:
            success, message = self.db.delete_cash_box(self.selected_cash_box)
            if success:
                messagebox.showinfo("نجح", message)
                self.refresh_cash_boxes()
                self.selected_cash_box = None
            else:
                messagebox.showerror("خطأ", message)

    # ==================== الوظائف المؤقتة للصفحات المتبقية ====================

    def show_items_pro(self):
        """عرض إدارة السلع الاحترافية"""
        self.clear_content()
        self.update_status("عرض إدارة السلع")

        # عنوان الصفحة
        title = tk.Label(self.content_frame, text="📦 إدارة السلع",
                        font=('Arial', 18, 'bold'), bg='white', fg='#2c3e50')
        title.pack(pady=20)

        # شريط البحث
        search_var, search_entry = self.create_search_bar(self.content_frame, self.search_items)

        # أزرار العمليات
        add_btn, edit_btn, delete_btn, refresh_btn = self.create_action_buttons(
            self.content_frame, self.add_item_dialog, self.edit_item_dialog, self.delete_item
        )

        # جدول السلع
        items_frame = tk.LabelFrame(self.content_frame, text="قائمة السلع",
                                   font=('Arial', 12, 'bold'), bg='white')
        items_frame.pack(fill='both', expand=True, padx=20, pady=10)

        columns = ('كود السلعة', 'اسم السلعة', 'السعر', 'الوصف')
        self.items_tree = ttk.Treeview(items_frame, columns=columns, show='headings')

        # تنسيق الأعمدة
        for col in columns:
            self.items_tree.heading(col, text=col)
            if col == 'السعر':
                self.items_tree.column(col, width=120, anchor='e')
            elif col == 'الوصف':
                self.items_tree.column(col, width=250, anchor='w')
            else:
                self.items_tree.column(col, width=150, anchor='center')

        # ربط الأحداث
        self.items_tree.bind('<ButtonRelease-1>', self.on_item_select)
        self.items_tree.bind('<Double-1>', self.edit_item_dialog)

        self.items_tree.pack(fill='both', expand=True, padx=10, pady=10)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(items_frame, orient='vertical', command=self.items_tree.yview)
        self.items_tree.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side='right', fill='y')

        # إحصائيات السلع
        self.items_stats_label = tk.Label(self.content_frame, text="",
                                         font=('Arial', 12, 'bold'), bg='white', fg='#e74c3c')
        self.items_stats_label.pack(pady=10)

        # تحديث البيانات
        self.refresh_items()

        # ربط زر التحديث
        refresh_btn.config(command=self.refresh_items)

    def refresh_items(self):
        """تحديث قائمة السلع"""
        # مسح البيانات الحالية
        for item in self.items_tree.get_children():
            self.items_tree.delete(item)

        # إضافة البيانات الجديدة
        items_df = self.db.get_items()
        total_value = 0

        for _, row in items_df.iterrows():
            price = row['item_price']
            total_value += price
            self.items_tree.insert('', 'end', values=(
                row['item_code'], row['item_name'],
                f"{price:,.2f}", row['item_description']
            ))

        # تحديث الإحصائيات
        avg_price = total_value/len(items_df) if len(items_df) > 0 else 0
        self.items_stats_label.config(text=f"📦 إجمالي عدد السلع: {len(items_df)} | متوسط السعر: {avg_price:,.2f}")
        self.update_status(f"تم تحديث {len(items_df)} سلعة")

    def search_items(self, search_term):
        """البحث في السلع"""
        # مسح البيانات الحالية
        for item in self.items_tree.get_children():
            self.items_tree.delete(item)

        # البحث وعرض النتائج
        items_df = self.db.get_items()
        if search_term:
            mask = (items_df['item_name'].str.contains(search_term, case=False, na=False) |
                    items_df['item_code'].str.contains(search_term, case=False, na=False) |
                    items_df['item_description'].str.contains(search_term, case=False, na=False))
            items_df = items_df[mask]

        total_value = 0
        for _, row in items_df.iterrows():
            price = row['item_price']
            total_value += price
            self.items_tree.insert('', 'end', values=(
                row['item_code'], row['item_name'],
                f"{price:,.2f}", row['item_description']
            ))

        self.items_stats_label.config(text=f"📦 نتائج البحث: {len(items_df)} سلعة")
        self.update_status(f"تم العثور على {len(items_df)} سلعة")

    def on_item_select(self, event):
        """عند تحديد سلعة"""
        selection = self.items_tree.selection()
        if selection:
            item = self.items_tree.item(selection[0])
            self.selected_item = item['values'][0]  # كود السلعة
            self.update_status(f"تم تحديد السلعة: {self.selected_item}")

    def add_item_dialog(self):
        """نافذة إضافة سلعة جديدة"""
        dialog = tk.Toplevel(self.root)
        dialog.title("إضافة سلعة جديدة")
        dialog.geometry("600x500")
        dialog.configure(bg='white')
        dialog.transient(self.root)
        dialog.grab_set()

        # عنوان النافذة
        title = tk.Label(dialog, text="➕ إضافة سلعة جديدة",
                        font=('Arial', 16, 'bold'), bg='white', fg='#2c3e50')
        title.pack(pady=20)

        # إطار الحقول
        fields_frame = tk.Frame(dialog, bg='white')
        fields_frame.pack(fill='both', expand=True, padx=30, pady=20)

        # حقول الإدخال
        tk.Label(fields_frame, text="كود السلعة:", font=('Arial', 12, 'bold'), bg='white').grid(row=0, column=0, sticky='w', pady=10)
        code_entry = tk.Entry(fields_frame, font=('Arial', 12), width=20)
        code_entry.grid(row=0, column=1, padx=10, pady=10, sticky='ew')

        tk.Label(fields_frame, text="اسم السلعة:", font=('Arial', 12, 'bold'), bg='white').grid(row=1, column=0, sticky='w', pady=10)
        name_entry = tk.Entry(fields_frame, font=('Arial', 12), width=30)
        name_entry.grid(row=1, column=1, padx=10, pady=10, sticky='ew')

        tk.Label(fields_frame, text="سعر السلعة:", font=('Arial', 12, 'bold'), bg='white').grid(row=2, column=0, sticky='w', pady=10)
        price_entry = tk.Entry(fields_frame, font=('Arial', 12), width=20)
        price_entry.insert(0, "0.0")
        price_entry.grid(row=2, column=1, padx=10, pady=10, sticky='ew')

        tk.Label(fields_frame, text="وصف السلعة:", font=('Arial', 12, 'bold'), bg='white').grid(row=3, column=0, sticky='nw', pady=10)
        desc_text = tk.Text(fields_frame, font=('Arial', 12), width=40, height=5)
        desc_text.grid(row=3, column=1, padx=10, pady=10, sticky='ew')

        fields_frame.grid_columnconfigure(1, weight=1)

        # أزرار العمليات
        buttons_frame = tk.Frame(dialog, bg='white')
        buttons_frame.pack(fill='x', padx=30, pady=20)

        def save_item():
            code = code_entry.get().strip()
            name = name_entry.get().strip()
            desc = desc_text.get("1.0", tk.END).strip()
            try:
                price = float(price_entry.get().strip())
            except ValueError:
                messagebox.showerror("خطأ", "السعر يجب أن يكون رقماً")
                return

            if not code or not name:
                messagebox.showerror("خطأ", "يرجى ملء الحقول المطلوبة")
                return

            if self.db.add_item(code, name, price, desc):
                messagebox.showinfo("نجح", "تم إضافة السلعة بنجاح")
                dialog.destroy()
                self.refresh_items()
            else:
                messagebox.showerror("خطأ", "كود السلعة موجود مسبقاً")

        save_btn = tk.Button(buttons_frame, text="💾 حفظ", command=save_item,
                            bg='#27ae60', fg='white', font=('Arial', 12, 'bold'),
                            relief='flat', padx=20, pady=8, cursor='hand2')
        save_btn.pack(side='left', padx=5)

        cancel_btn = tk.Button(buttons_frame, text="❌ إلغاء", command=dialog.destroy,
                              bg='#e74c3c', fg='white', font=('Arial', 12, 'bold'),
                              relief='flat', padx=20, pady=8, cursor='hand2')
        cancel_btn.pack(side='right', padx=5)

        # التركيز على أول حقل
        code_entry.focus()

    def edit_item_dialog(self, event=None):
        """نافذة تعديل سلعة"""
        if not self.selected_item:
            messagebox.showwarning("تحذير", "يرجى تحديد سلعة للتعديل")
            return

        # الحصول على بيانات السلعة
        item_data = self.db.get_item_by_code(self.selected_item)
        if not item_data:
            messagebox.showerror("خطأ", "لم يتم العثور على السلعة")
            return

        dialog = tk.Toplevel(self.root)
        dialog.title("تعديل السلعة")
        dialog.geometry("600x500")
        dialog.configure(bg='white')
        dialog.transient(self.root)
        dialog.grab_set()

        # عنوان النافذة
        title = tk.Label(dialog, text="✏️ تعديل السلعة",
                        font=('Arial', 16, 'bold'), bg='white', fg='#2c3e50')
        title.pack(pady=20)

        # إطار الحقول
        fields_frame = tk.Frame(dialog, bg='white')
        fields_frame.pack(fill='both', expand=True, padx=30, pady=20)

        # حقول الإدخال مع البيانات الحالية
        tk.Label(fields_frame, text="كود السلعة:", font=('Arial', 12, 'bold'), bg='white').grid(row=0, column=0, sticky='w', pady=10)
        code_entry = tk.Entry(fields_frame, font=('Arial', 12), width=20)
        code_entry.insert(0, item_data[0])  # item_code
        code_entry.grid(row=0, column=1, padx=10, pady=10, sticky='ew')

        tk.Label(fields_frame, text="اسم السلعة:", font=('Arial', 12, 'bold'), bg='white').grid(row=1, column=0, sticky='w', pady=10)
        name_entry = tk.Entry(fields_frame, font=('Arial', 12), width=30)
        name_entry.insert(0, item_data[1])  # item_name
        name_entry.grid(row=1, column=1, padx=10, pady=10, sticky='ew')

        tk.Label(fields_frame, text="سعر السلعة:", font=('Arial', 12, 'bold'), bg='white').grid(row=2, column=0, sticky='w', pady=10)
        price_entry = tk.Entry(fields_frame, font=('Arial', 12), width=20)
        price_entry.insert(0, str(item_data[2]))  # item_price
        price_entry.grid(row=2, column=1, padx=10, pady=10, sticky='ew')

        tk.Label(fields_frame, text="وصف السلعة:", font=('Arial', 12, 'bold'), bg='white').grid(row=3, column=0, sticky='nw', pady=10)
        desc_text = tk.Text(fields_frame, font=('Arial', 12), width=40, height=5)
        desc_text.insert("1.0", item_data[3])  # item_description
        desc_text.grid(row=3, column=1, padx=10, pady=10, sticky='ew')

        fields_frame.grid_columnconfigure(1, weight=1)

        # أزرار العمليات
        buttons_frame = tk.Frame(dialog, bg='white')
        buttons_frame.pack(fill='x', padx=30, pady=20)

        def update_item():
            new_code = code_entry.get().strip()
            name = name_entry.get().strip()
            desc = desc_text.get("1.0", tk.END).strip()
            try:
                price = float(price_entry.get().strip())
            except ValueError:
                messagebox.showerror("خطأ", "السعر يجب أن يكون رقماً")
                return

            if not new_code or not name:
                messagebox.showerror("خطأ", "يرجى ملء الحقول المطلوبة")
                return

            if self.db.update_item(self.selected_item, new_code, name, price, desc):
                messagebox.showinfo("نجح", "تم تحديث السلعة بنجاح")
                dialog.destroy()
                self.refresh_items()
                self.selected_item = new_code
            else:
                messagebox.showerror("خطأ", "فشل في تحديث السلعة")

        update_btn = tk.Button(buttons_frame, text="💾 تحديث", command=update_item,
                              bg='#f39c12', fg='white', font=('Arial', 12, 'bold'),
                              relief='flat', padx=20, pady=8, cursor='hand2')
        update_btn.pack(side='left', padx=5)

        cancel_btn = tk.Button(buttons_frame, text="❌ إلغاء", command=dialog.destroy,
                              bg='#e74c3c', fg='white', font=('Arial', 12, 'bold'),
                              relief='flat', padx=20, pady=8, cursor='hand2')
        cancel_btn.pack(side='right', padx=5)

    def delete_item(self):
        """حذف سلعة"""
        if not self.selected_item:
            messagebox.showwarning("تحذير", "يرجى تحديد سلعة للحذف")
            return

        # تأكيد الحذف
        result = messagebox.askyesno("تأكيد الحذف",
                                   f"هل أنت متأكد من حذف السلعة '{self.selected_item}'؟\n"
                                   "هذا الإجراء لا يمكن التراجع عنه!")

        if result:
            success, message = self.db.delete_item(self.selected_item)
            if success:
                messagebox.showinfo("نجح", message)
                self.refresh_items()
                self.selected_item = None
            else:
                messagebox.showerror("خطأ", message)

    def show_journal_entries_pro(self):
        """عرض إدارة القيود الاحترافية"""
        self.clear_content()
        self.update_status("عرض إدارة القيود")

        # عنوان الصفحة
        title = tk.Label(self.content_frame, text="📝 إدارة القيود المحاسبية",
                        font=('Arial', 18, 'bold'), bg='white', fg='#2c3e50')
        title.pack(pady=20)

        # شريط البحث
        search_var, search_entry = self.create_search_bar(self.content_frame, self.search_journal_entries)

        # أزرار العمليات
        add_btn, edit_btn, delete_btn, refresh_btn = self.create_action_buttons(
            self.content_frame, self.add_journal_entry_dialog, self.edit_journal_entry_dialog, self.delete_journal_entry
        )

        # جدول القيود
        entries_frame = tk.LabelFrame(self.content_frame, text="قائمة القيود المحاسبية",
                                     font=('Arial', 12, 'bold'), bg='white')
        entries_frame.pack(fill='both', expand=True, padx=20, pady=10)

        columns = ('رقم القيد', 'التاريخ', 'الوصف', 'إجمالي المدين', 'إجمالي الدائن', 'الحالة')
        self.entries_tree = ttk.Treeview(entries_frame, columns=columns, show='headings')

        # تنسيق الأعمدة
        for col in columns:
            self.entries_tree.heading(col, text=col)
            if col in ['إجمالي المدين', 'إجمالي الدائن']:
                self.entries_tree.column(col, width=120, anchor='e')
            elif col == 'رقم القيد':
                self.entries_tree.column(col, width=80, anchor='center')
            elif col == 'التاريخ':
                self.entries_tree.column(col, width=100, anchor='center')
            elif col == 'الحالة':
                self.entries_tree.column(col, width=80, anchor='center')
            else:
                self.entries_tree.column(col, width=200, anchor='w')

        # ربط الأحداث
        self.entries_tree.bind('<ButtonRelease-1>', self.on_journal_entry_select)
        self.entries_tree.bind('<Double-1>', self.view_journal_entry_details)

        self.entries_tree.pack(fill='both', expand=True, padx=10, pady=10)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(entries_frame, orient='vertical', command=self.entries_tree.yview)
        self.entries_tree.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side='right', fill='y')

        # إحصائيات القيود
        self.entries_stats_label = tk.Label(self.content_frame, text="",
                                           font=('Arial', 12, 'bold'), bg='white', fg='#9b59b6')
        self.entries_stats_label.pack(pady=10)

        # تحديث البيانات
        self.refresh_journal_entries()

        # ربط زر التحديث
        refresh_btn.config(command=self.refresh_journal_entries)

    def refresh_journal_entries(self):
        """تحديث قائمة القيود"""
        # مسح البيانات الحالية
        for item in self.entries_tree.get_children():
            self.entries_tree.delete(item)

        # الحصول على القيود مع الإجماليات
        conn = self.db.get_connection()
        query = '''
            SELECT je.entry_id, je.entry_date, je.description,
                   SUM(ed.debit_amount) as total_debit,
                   SUM(ed.credit_amount) as total_credit,
                   CASE
                       WHEN SUM(ed.debit_amount) = SUM(ed.credit_amount) THEN 'متوازن'
                       ELSE 'غير متوازن'
                   END as status
            FROM journal_entries je
            LEFT JOIN entry_details ed ON je.entry_id = ed.entry_id
            GROUP BY je.entry_id, je.entry_date, je.description
            ORDER BY je.entry_id DESC
        '''
        entries_df = pd.read_sql_query(query, conn)
        conn.close()

        total_entries = len(entries_df)
        balanced_entries = len(entries_df[entries_df['status'] == 'متوازن'])

        for _, row in entries_df.iterrows():
            debit = row['total_debit'] if pd.notna(row['total_debit']) else 0
            credit = row['total_credit'] if pd.notna(row['total_credit']) else 0

            self.entries_tree.insert('', 'end', values=(
                row['entry_id'],
                row['entry_date'],
                row['description'][:40] + "..." if len(row['description']) > 40 else row['description'],
                f"{debit:,.2f}",
                f"{credit:,.2f}",
                row['status']
            ))

        # تحديث الإحصائيات
        self.entries_stats_label.config(
            text=f"📝 إجمالي القيود: {total_entries} | القيود المتوازنة: {balanced_entries} | غير المتوازنة: {total_entries - balanced_entries}"
        )
        self.update_status(f"تم تحديث {total_entries} قيد محاسبي")

    def search_journal_entries(self, search_term):
        """البحث في القيود"""
        # مسح البيانات الحالية
        for item in self.entries_tree.get_children():
            self.entries_tree.delete(item)

        # البحث في القيود
        conn = self.db.get_connection()
        if search_term:
            query = '''
                SELECT je.entry_id, je.entry_date, je.description,
                       SUM(ed.debit_amount) as total_debit,
                       SUM(ed.credit_amount) as total_credit,
                       CASE
                           WHEN SUM(ed.debit_amount) = SUM(ed.credit_amount) THEN 'متوازن'
                           ELSE 'غير متوازن'
                       END as status
                FROM journal_entries je
                LEFT JOIN entry_details ed ON je.entry_id = ed.entry_id
                WHERE je.description LIKE ? OR je.entry_id LIKE ?
                GROUP BY je.entry_id, je.entry_date, je.description
                ORDER BY je.entry_id DESC
            '''
            entries_df = pd.read_sql_query(query, conn, params=[f'%{search_term}%', f'%{search_term}%'])
        else:
            query = '''
                SELECT je.entry_id, je.entry_date, je.description,
                       SUM(ed.debit_amount) as total_debit,
                       SUM(ed.credit_amount) as total_credit,
                       CASE
                           WHEN SUM(ed.debit_amount) = SUM(ed.credit_amount) THEN 'متوازن'
                           ELSE 'غير متوازن'
                       END as status
                FROM journal_entries je
                LEFT JOIN entry_details ed ON je.entry_id = ed.entry_id
                GROUP BY je.entry_id, je.entry_date, je.description
                ORDER BY je.entry_id DESC
            '''
            entries_df = pd.read_sql_query(query, conn)
        conn.close()

        for _, row in entries_df.iterrows():
            debit = row['total_debit'] if pd.notna(row['total_debit']) else 0
            credit = row['total_credit'] if pd.notna(row['total_credit']) else 0

            self.entries_tree.insert('', 'end', values=(
                row['entry_id'],
                row['entry_date'],
                row['description'][:40] + "..." if len(row['description']) > 40 else row['description'],
                f"{debit:,.2f}",
                f"{credit:,.2f}",
                row['status']
            ))

        self.entries_stats_label.config(text=f"📝 نتائج البحث: {len(entries_df)} قيد")
        self.update_status(f"تم العثور على {len(entries_df)} قيد")

    def on_journal_entry_select(self, event):
        """عند تحديد قيد"""
        selection = self.entries_tree.selection()
        if selection:
            item = self.entries_tree.item(selection[0])
            self.selected_entry = item['values'][0]  # رقم القيد
            self.update_status(f"تم تحديد القيد: {self.selected_entry}")

    def add_journal_entry_dialog(self):
        """نافذة إضافة قيد جديد"""
        dialog = tk.Toplevel(self.root)
        dialog.title("إضافة قيد محاسبي جديد")
        dialog.geometry("800x600")
        dialog.configure(bg='white')
        dialog.transient(self.root)
        dialog.grab_set()

        # عنوان النافذة
        title = tk.Label(dialog, text="➕ إضافة قيد محاسبي جديد",
                        font=('Arial', 16, 'bold'), bg='white', fg='#2c3e50')
        title.pack(pady=20)

        # إطار المعلومات الأساسية
        info_frame = tk.LabelFrame(dialog, text="معلومات القيد", font=('Arial', 12, 'bold'), bg='white')
        info_frame.pack(fill='x', padx=20, pady=10)

        # التاريخ والوصف
        tk.Label(info_frame, text="التاريخ:", font=('Arial', 11, 'bold'), bg='white').grid(row=0, column=0, sticky='w', padx=10, pady=5)
        date_entry = tk.Entry(info_frame, font=('Arial', 11), width=15)
        date_entry.insert(0, date.today().strftime('%Y-%m-%d'))
        date_entry.grid(row=0, column=1, padx=10, pady=5)

        tk.Label(info_frame, text="الوصف:", font=('Arial', 11, 'bold'), bg='white').grid(row=0, column=2, sticky='w', padx=10, pady=5)
        desc_entry = tk.Entry(info_frame, font=('Arial', 11), width=40)
        desc_entry.grid(row=0, column=3, padx=10, pady=5, sticky='ew')

        info_frame.grid_columnconfigure(3, weight=1)

        # إطار تفاصيل القيد
        details_frame = tk.LabelFrame(dialog, text="تفاصيل القيد", font=('Arial', 12, 'bold'), bg='white')
        details_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # جدول التفاصيل
        columns = ('الحساب', 'مدين', 'دائن')
        details_tree = ttk.Treeview(details_frame, columns=columns, show='headings', height=8)

        for col in columns:
            details_tree.heading(col, text=col)
            if col in ['مدين', 'دائن']:
                details_tree.column(col, width=100, anchor='e')
            else:
                details_tree.column(col, width=200, anchor='w')

        details_tree.pack(fill='both', expand=True, padx=10, pady=5)

        # إطار إضافة سطر
        add_line_frame = tk.Frame(details_frame, bg='white')
        add_line_frame.pack(fill='x', padx=10, pady=5)

        # قائمة الحسابات
        accounts_df = self.db.get_accounts()
        account_names = [f"{row['account_code']} - {row['account_name']}" for _, row in accounts_df.iterrows()]

        tk.Label(add_line_frame, text="الحساب:", font=('Arial', 10, 'bold'), bg='white').grid(row=0, column=0, padx=5)
        account_combo = ttk.Combobox(add_line_frame, values=account_names, width=25, font=('Arial', 10))
        account_combo.grid(row=0, column=1, padx=5)

        tk.Label(add_line_frame, text="مدين:", font=('Arial', 10, 'bold'), bg='white').grid(row=0, column=2, padx=5)
        debit_entry = tk.Entry(add_line_frame, font=('Arial', 10), width=12)
        debit_entry.insert(0, "0.0")
        debit_entry.grid(row=0, column=3, padx=5)

        tk.Label(add_line_frame, text="دائن:", font=('Arial', 10, 'bold'), bg='white').grid(row=0, column=4, padx=5)
        credit_entry = tk.Entry(add_line_frame, font=('Arial', 10), width=12)
        credit_entry.insert(0, "0.0")
        credit_entry.grid(row=0, column=5, padx=5)

        def add_line():
            account = account_combo.get()
            try:
                debit = float(debit_entry.get())
                credit = float(credit_entry.get())
            except ValueError:
                messagebox.showerror("خطأ", "المبالغ يجب أن تكون أرقاماً")
                return

            if not account:
                messagebox.showerror("خطأ", "يرجى اختيار حساب")
                return

            if debit == 0 and credit == 0:
                messagebox.showerror("خطأ", "يجب إدخال مبلغ في المدين أو الدائن")
                return

            if debit > 0 and credit > 0:
                messagebox.showerror("خطأ", "لا يمكن أن يكون للحساب مبلغ في المدين والدائن معاً")
                return

            details_tree.insert('', 'end', values=(account, f"{debit:,.2f}", f"{credit:,.2f}"))

            # مسح الحقول
            account_combo.set('')
            debit_entry.delete(0, tk.END)
            debit_entry.insert(0, "0.0")
            credit_entry.delete(0, tk.END)
            credit_entry.insert(0, "0.0")

            update_totals()

        add_line_btn = tk.Button(add_line_frame, text="إضافة سطر", command=add_line,
                                bg='#3498db', fg='white', font=('Arial', 10, 'bold'),
                                relief='flat', padx=15, pady=5, cursor='hand2')
        add_line_btn.grid(row=0, column=6, padx=10)

        # إجماليات
        totals_frame = tk.Frame(details_frame, bg='white')
        totals_frame.pack(fill='x', padx=10, pady=5)

        total_debit_label = tk.Label(totals_frame, text="إجمالي المدين: 0.00",
                                    font=('Arial', 11, 'bold'), bg='white', fg='#e74c3c')
        total_debit_label.pack(side='left', padx=20)

        total_credit_label = tk.Label(totals_frame, text="إجمالي الدائن: 0.00",
                                     font=('Arial', 11, 'bold'), bg='white', fg='#27ae60')
        total_credit_label.pack(side='left', padx=20)

        balance_label = tk.Label(totals_frame, text="الفرق: 0.00",
                                font=('Arial', 11, 'bold'), bg='white', fg='#f39c12')
        balance_label.pack(side='left', padx=20)

        def update_totals():
            total_debit = 0
            total_credit = 0

            for item in details_tree.get_children():
                values = details_tree.item(item)['values']
                total_debit += float(values[1].replace(',', ''))
                total_credit += float(values[2].replace(',', ''))

            total_debit_label.config(text=f"إجمالي المدين: {total_debit:,.2f}")
            total_credit_label.config(text=f"إجمالي الدائن: {total_credit:,.2f}")

            difference = abs(total_debit - total_credit)
            balance_label.config(text=f"الفرق: {difference:,.2f}")

            if difference == 0 and total_debit > 0:
                balance_label.config(fg='#27ae60', text="متوازن ✓")
            elif difference > 0:
                balance_label.config(fg='#e74c3c', text=f"غير متوازن: {difference:,.2f}")
            else:
                balance_label.config(fg='#f39c12', text="الفرق: 0.00")

        # أزرار العمليات
        buttons_frame = tk.Frame(dialog, bg='white')
        buttons_frame.pack(fill='x', padx=20, pady=20)

        def save_journal_entry():
            entry_date = date_entry.get().strip()
            description = desc_entry.get().strip()

            if not entry_date or not description:
                messagebox.showerror("خطأ", "يرجى ملء التاريخ والوصف")
                return

            # التحقق من وجود تفاصيل
            if not details_tree.get_children():
                messagebox.showerror("خطأ", "يرجى إضافة تفاصيل القيد")
                return

            # التحقق من التوازن
            total_debit = 0
            total_credit = 0

            for item in details_tree.get_children():
                values = details_tree.item(item)['values']
                total_debit += float(values[1].replace(',', ''))
                total_credit += float(values[2].replace(',', ''))

            if abs(total_debit - total_credit) > 0.01:  # تسامح صغير للأخطاء العشرية
                result = messagebox.askyesno("تحذير",
                                           f"القيد غير متوازن!\nالمدين: {total_debit:,.2f}\nالدائن: {total_credit:,.2f}\n"
                                           "هل تريد الحفظ رغم ذلك؟")
                if not result:
                    return

            # حفظ القيد
            try:
                entry_id = self.db.add_journal_entry(entry_date, description)

                # حفظ التفاصيل
                for item in details_tree.get_children():
                    values = details_tree.item(item)['values']
                    account_info = values[0].split(' - ')
                    account_code = account_info[0]
                    debit = float(values[1].replace(',', ''))
                    credit = float(values[2].replace(',', ''))

                    self.db.add_entry_detail(entry_id, account_code, debit, credit)

                messagebox.showinfo("نجح", f"تم حفظ القيد رقم {entry_id} بنجاح")
                dialog.destroy()
                self.refresh_journal_entries()

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حفظ القيد: {e}")

        save_btn = tk.Button(buttons_frame, text="💾 حفظ القيد", command=save_journal_entry,
                            bg='#27ae60', fg='white', font=('Arial', 12, 'bold'),
                            relief='flat', padx=20, pady=8, cursor='hand2')
        save_btn.pack(side='left', padx=5)

        cancel_btn = tk.Button(buttons_frame, text="❌ إلغاء", command=dialog.destroy,
                              bg='#e74c3c', fg='white', font=('Arial', 12, 'bold'),
                              relief='flat', padx=20, pady=8, cursor='hand2')
        cancel_btn.pack(side='right', padx=5)

        # التركيز على حقل الوصف
        desc_entry.focus()

    def edit_journal_entry_dialog(self, event=None):
        """نافذة تعديل قيد (عرض فقط)"""
        if not self.selected_entry:
            messagebox.showwarning("تحذير", "يرجى تحديد قيد للعرض")
            return

        self.view_journal_entry_details()

    def view_journal_entry_details(self, event=None):
        """عرض تفاصيل القيد"""
        if not self.selected_entry:
            messagebox.showwarning("تحذير", "يرجى تحديد قيد للعرض")
            return

        # الحصول على تفاصيل القيد
        details_df = self.db.get_journal_entry_details(self.selected_entry)
        if details_df.empty:
            messagebox.showerror("خطأ", "لم يتم العثور على تفاصيل القيد")
            return

        dialog = tk.Toplevel(self.root)
        dialog.title(f"تفاصيل القيد رقم {self.selected_entry}")
        dialog.geometry("700x500")
        dialog.configure(bg='white')
        dialog.transient(self.root)
        dialog.grab_set()

        # عنوان النافذة
        title = tk.Label(dialog, text=f"📋 تفاصيل القيد رقم {self.selected_entry}",
                        font=('Arial', 16, 'bold'), bg='white', fg='#2c3e50')
        title.pack(pady=20)

        # معلومات القيد
        info_frame = tk.LabelFrame(dialog, text="معلومات القيد", font=('Arial', 12, 'bold'), bg='white')
        info_frame.pack(fill='x', padx=20, pady=10)

        first_row = details_df.iloc[0]
        tk.Label(info_frame, text=f"التاريخ: {first_row['entry_date']}",
                font=('Arial', 11), bg='white').pack(anchor='w', padx=10, pady=2)
        tk.Label(info_frame, text=f"الوصف: {first_row['description']}",
                font=('Arial', 11), bg='white').pack(anchor='w', padx=10, pady=2)

        # جدول التفاصيل
        details_frame = tk.LabelFrame(dialog, text="تفاصيل القيد", font=('Arial', 12, 'bold'), bg='white')
        details_frame.pack(fill='both', expand=True, padx=20, pady=10)

        columns = ('الحساب', 'اسم الحساب', 'مدين', 'دائن')
        details_tree = ttk.Treeview(details_frame, columns=columns, show='headings')

        for col in columns:
            details_tree.heading(col, text=col)
            if col in ['مدين', 'دائن']:
                details_tree.column(col, width=100, anchor='e')
            elif col == 'الحساب':
                details_tree.column(col, width=100, anchor='center')
            else:
                details_tree.column(col, width=200, anchor='w')

        details_tree.pack(fill='both', expand=True, padx=10, pady=10)

        # إضافة البيانات
        total_debit = 0
        total_credit = 0

        for _, row in details_df.iterrows():
            debit = row['debit_amount']
            credit = row['credit_amount']
            total_debit += debit
            total_credit += credit

            details_tree.insert('', 'end', values=(
                row['account_code'],
                row['account_name'],
                f"{debit:,.2f}" if debit > 0 else "",
                f"{credit:,.2f}" if credit > 0 else ""
            ))

        # الإجماليات
        totals_frame = tk.Frame(details_frame, bg='white')
        totals_frame.pack(fill='x', padx=10, pady=5)

        tk.Label(totals_frame, text=f"إجمالي المدين: {total_debit:,.2f}",
                font=('Arial', 11, 'bold'), bg='white', fg='#e74c3c').pack(side='left', padx=20)
        tk.Label(totals_frame, text=f"إجمالي الدائن: {total_credit:,.2f}",
                font=('Arial', 11, 'bold'), bg='white', fg='#27ae60').pack(side='left', padx=20)

        difference = abs(total_debit - total_credit)
        if difference < 0.01:
            status_text = "متوازن ✓"
            status_color = '#27ae60'
        else:
            status_text = f"غير متوازن: {difference:,.2f}"
            status_color = '#e74c3c'

        tk.Label(totals_frame, text=status_text,
                font=('Arial', 11, 'bold'), bg='white', fg=status_color).pack(side='left', padx=20)

        # زر الإغلاق
        close_btn = tk.Button(dialog, text="إغلاق", command=dialog.destroy,
                             bg='#95a5a6', fg='white', font=('Arial', 12, 'bold'),
                             relief='flat', padx=20, pady=8, cursor='hand2')
        close_btn.pack(pady=20)

    def delete_journal_entry(self):
        """حذف قيد محاسبي"""
        if not self.selected_entry:
            messagebox.showwarning("تحذير", "يرجى تحديد قيد للحذف")
            return

        # تأكيد الحذف
        result = messagebox.askyesno("تأكيد الحذف",
                                   f"هل أنت متأكد من حذف القيد رقم '{self.selected_entry}'؟\n"
                                   "سيتم حذف القيد وجميع تفاصيله!\n"
                                   "هذا الإجراء لا يمكن التراجع عنه!")

        if result:
            success, message = self.db.delete_journal_entry(self.selected_entry)
            if success:
                messagebox.showinfo("نجح", message)
                self.refresh_journal_entries()
                self.selected_entry = None
            else:
                messagebox.showerror("خطأ", message)

    def show_statements_pro(self):
        """عرض كشوف الحسابات الاحترافية"""
        self.clear_content()
        self.update_status("عرض كشوف الحسابات")

        # عنوان الصفحة
        title = tk.Label(self.content_frame, text="📊 كشوف الحسابات",
                        font=('Arial', 18, 'bold'), bg='white', fg='#2c3e50')
        title.pack(pady=20)

        # إطار الفلاتر
        filters_frame = tk.LabelFrame(self.content_frame, text="فلاتر البحث",
                                     font=('Arial', 12, 'bold'), bg='white')
        filters_frame.pack(fill='x', padx=20, pady=10)

        # اختيار الحساب
        tk.Label(filters_frame, text="الحساب:", font=('Arial', 11, 'bold'), bg='white').grid(row=0, column=0, padx=10, pady=5, sticky='w')

        accounts_df = self.db.get_accounts()
        account_options = ["جميع الحسابات"] + [f"{row['account_code']} - {row['account_name']}" for _, row in accounts_df.iterrows()]

        self.account_filter = ttk.Combobox(filters_frame, values=account_options, width=30, font=('Arial', 10))
        self.account_filter.set("جميع الحسابات")
        self.account_filter.grid(row=0, column=1, padx=10, pady=5)

        # فترة التاريخ
        tk.Label(filters_frame, text="من تاريخ:", font=('Arial', 11, 'bold'), bg='white').grid(row=0, column=2, padx=10, pady=5, sticky='w')
        self.from_date = tk.Entry(filters_frame, font=('Arial', 10), width=12)
        self.from_date.insert(0, "2024-01-01")
        self.from_date.grid(row=0, column=3, padx=5, pady=5)

        tk.Label(filters_frame, text="إلى تاريخ:", font=('Arial', 11, 'bold'), bg='white').grid(row=0, column=4, padx=10, pady=5, sticky='w')
        self.to_date = tk.Entry(filters_frame, font=('Arial', 10), width=12)
        self.to_date.insert(0, date.today().strftime('%Y-%m-%d'))
        self.to_date.grid(row=0, column=5, padx=5, pady=5)

        # زر التطبيق
        apply_btn = tk.Button(filters_frame, text="🔍 عرض الكشف", command=self.generate_statement,
                             bg='#3498db', fg='white', font=('Arial', 11, 'bold'),
                             relief='flat', padx=15, pady=5, cursor='hand2')
        apply_btn.grid(row=0, column=6, padx=15, pady=5)

        # جدول الكشف
        statement_frame = tk.LabelFrame(self.content_frame, text="كشف الحساب",
                                       font=('Arial', 12, 'bold'), bg='white')
        statement_frame.pack(fill='both', expand=True, padx=20, pady=10)

        columns = ('التاريخ', 'رقم القيد', 'الوصف', 'مدين', 'دائن', 'الرصيد')
        self.statement_tree = ttk.Treeview(statement_frame, columns=columns, show='headings')

        # تنسيق الأعمدة
        for col in columns:
            self.statement_tree.heading(col, text=col)
            if col in ['مدين', 'دائن', 'الرصيد']:
                self.statement_tree.column(col, width=100, anchor='e')
            elif col in ['التاريخ', 'رقم القيد']:
                self.statement_tree.column(col, width=100, anchor='center')
            else:
                self.statement_tree.column(col, width=200, anchor='w')

        self.statement_tree.pack(fill='both', expand=True, padx=10, pady=10)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(statement_frame, orient='vertical', command=self.statement_tree.yview)
        self.statement_tree.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side='right', fill='y')

        # ملخص الكشف
        self.statement_summary = tk.Label(self.content_frame, text="",
                                         font=('Arial', 12, 'bold'), bg='white', fg='#1abc9c')
        self.statement_summary.pack(pady=10)

        # أزرار العمليات
        buttons_frame = tk.Frame(self.content_frame, bg='white')
        buttons_frame.pack(fill='x', padx=20, pady=10)

        export_btn = tk.Button(buttons_frame, text="📤 تصدير إلى Excel", command=self.export_statement,
                              bg='#27ae60', fg='white', font=('Arial', 11, 'bold'),
                              relief='flat', padx=20, pady=8, cursor='hand2')
        export_btn.pack(side='left', padx=5)

        print_btn = tk.Button(buttons_frame, text="🖨️ طباعة", command=self.print_statement,
                             bg='#9b59b6', fg='white', font=('Arial', 11, 'bold'),
                             relief='flat', padx=20, pady=8, cursor='hand2')
        print_btn.pack(side='left', padx=5)

    def generate_statement(self):
        """إنشاء كشف الحساب"""
        # مسح البيانات الحالية
        for item in self.statement_tree.get_children():
            self.statement_tree.delete(item)

        account_selection = self.account_filter.get()
        from_date = self.from_date.get()
        to_date = self.to_date.get()

        if account_selection == "جميع الحسابات":
            # عرض ملخص جميع الحسابات
            self.generate_all_accounts_summary(from_date, to_date)
        else:
            # عرض كشف حساب محدد
            account_code = account_selection.split(' - ')[0]
            self.generate_single_account_statement(account_code, from_date, to_date)

    def generate_single_account_statement(self, account_code, from_date, to_date):
        """إنشاء كشف حساب واحد"""
        conn = self.db.get_connection()

        # الحصول على اسم الحساب
        account_info = self.db.get_account_by_code(account_code)
        if not account_info:
            messagebox.showerror("خطأ", "لم يتم العثور على الحساب")
            return

        account_name = account_info[1]

        # الحصول على حركات الحساب
        query = '''
            SELECT je.entry_date, je.entry_id, je.description,
                   ed.debit_amount, ed.credit_amount
            FROM journal_entries je
            JOIN entry_details ed ON je.entry_id = ed.entry_id
            WHERE ed.account_code = ?
            AND je.entry_date BETWEEN ? AND ?
            ORDER BY je.entry_date, je.entry_id
        '''

        movements_df = pd.read_sql_query(query, conn, params=[account_code, from_date, to_date])
        conn.close()

        # حساب الرصيد الجاري
        running_balance = 0
        total_debit = 0
        total_credit = 0

        for _, row in movements_df.iterrows():
            debit = row['debit_amount']
            credit = row['credit_amount']

            total_debit += debit
            total_credit += credit
            running_balance += debit - credit

            self.statement_tree.insert('', 'end', values=(
                row['entry_date'],
                row['entry_id'],
                row['description'][:30] + "..." if len(row['description']) > 30 else row['description'],
                f"{debit:,.2f}" if debit > 0 else "",
                f"{credit:,.2f}" if credit > 0 else "",
                f"{running_balance:,.2f}"
            ))

        # تحديث الملخص
        self.statement_summary.config(
            text=f"📊 كشف حساب: {account_code} - {account_name} | "
                 f"إجمالي المدين: {total_debit:,.2f} | "
                 f"إجمالي الدائن: {total_credit:,.2f} | "
                 f"الرصيد النهائي: {running_balance:,.2f}"
        )

        self.update_status(f"تم إنشاء كشف حساب {account_code} - {len(movements_df)} حركة")

    def generate_all_accounts_summary(self, from_date, to_date):
        """إنشاء ملخص جميع الحسابات"""
        conn = self.db.get_connection()

        # الحصول على أرصدة جميع الحسابات
        query = '''
            SELECT a.account_code, a.account_name,
                   COALESCE(SUM(ed.debit_amount), 0) as total_debit,
                   COALESCE(SUM(ed.credit_amount), 0) as total_credit,
                   COALESCE(SUM(ed.debit_amount), 0) - COALESCE(SUM(ed.credit_amount), 0) as balance
            FROM accounts a
            LEFT JOIN entry_details ed ON a.account_code = ed.account_code
            LEFT JOIN journal_entries je ON ed.entry_id = je.entry_id
            WHERE je.entry_date IS NULL OR je.entry_date BETWEEN ? AND ?
            GROUP BY a.account_code, a.account_name
            ORDER BY a.account_code
        '''

        summary_df = pd.read_sql_query(query, conn, params=[from_date, to_date])
        conn.close()

        total_debit_all = 0
        total_credit_all = 0

        for _, row in summary_df.iterrows():
            debit = row['total_debit']
            credit = row['total_credit']
            balance = row['balance']

            total_debit_all += debit
            total_credit_all += credit

            self.statement_tree.insert('', 'end', values=(
                "",  # التاريخ
                row['account_code'],  # رقم القيد (نستخدمه لكود الحساب)
                row['account_name'],  # الوصف (نستخدمه لاسم الحساب)
                f"{debit:,.2f}" if debit > 0 else "",
                f"{credit:,.2f}" if credit > 0 else "",
                f"{balance:,.2f}"
            ))

        # تحديث الملخص
        self.statement_summary.config(
            text=f"📊 ملخص جميع الحسابات | "
                 f"إجمالي المدين: {total_debit_all:,.2f} | "
                 f"إجمالي الدائن: {total_credit_all:,.2f} | "
                 f"عدد الحسابات: {len(summary_df)}"
        )

        self.update_status(f"تم إنشاء ملخص {len(summary_df)} حساب")

    def export_statement(self):
        """تصدير الكشف إلى Excel"""
        if not self.statement_tree.get_children():
            messagebox.showwarning("تحذير", "لا توجد بيانات للتصدير")
            return

        # جمع البيانات
        data = []
        for item in self.statement_tree.get_children():
            values = self.statement_tree.item(item)['values']
            data.append(values)

        # إنشاء DataFrame
        columns = ['التاريخ', 'رقم القيد', 'الوصف', 'مدين', 'دائن', 'الرصيد']
        df = pd.DataFrame(data, columns=columns)

        # حفظ الملف
        filename = f"كشف_الحساب_{date.today().strftime('%Y%m%d')}.xlsx"
        try:
            df.to_excel(filename, index=False, engine='openpyxl')
            messagebox.showinfo("نجح", f"تم تصدير الكشف إلى {filename}")
            self.update_status(f"تم تصدير الكشف إلى {filename}")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في التصدير: {e}")

    def print_statement(self):
        """طباعة الكشف"""
        messagebox.showinfo("معلومات", "ميزة الطباعة ستكون متوفرة قريباً")

    def show_reports_pro(self):
        """عرض التقارير الاحترافية"""
        self.clear_content()
        self.update_status("عرض التقارير")

        # عنوان الصفحة
        title = tk.Label(self.content_frame, text="📈 التقارير المالية",
                        font=('Arial', 18, 'bold'), bg='white', fg='#2c3e50')
        title.pack(pady=20)

        # إطار التقارير المتاحة
        reports_frame = tk.LabelFrame(self.content_frame, text="التقارير المتاحة",
                                     font=('Arial', 12, 'bold'), bg='white')
        reports_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # شبكة التقارير
        reports_grid = tk.Frame(reports_frame, bg='white')
        reports_grid.pack(fill='both', expand=True, padx=20, pady=20)

        # تقارير الصف الأول
        self.create_report_card(reports_grid, 0, 0, "📊 ميزان المراجعة",
                               "عرض أرصدة جميع الحسابات", "#3498db", self.trial_balance_report)

        self.create_report_card(reports_grid, 0, 1, "💰 تقرير الصناديق",
                               "ملخص أرصدة الصناديق", "#27ae60", self.cash_boxes_report)

        self.create_report_card(reports_grid, 0, 2, "📦 تقرير السلع",
                               "قائمة السلع والأسعار", "#e74c3c", self.items_report)

        # تقارير الصف الثاني
        self.create_report_card(reports_grid, 1, 0, "📝 تقرير القيود",
                               "ملخص القيود المحاسبية", "#9b59b6", self.journal_entries_report)

        self.create_report_card(reports_grid, 1, 1, "📈 تقرير الحركة",
                               "حركة الحسابات خلال فترة", "#1abc9c", self.movement_report)

        self.create_report_card(reports_grid, 1, 2, "📋 تقرير شامل",
                               "تقرير شامل للنظام", "#34495e", self.comprehensive_report)

        # تكوين الشبكة
        for i in range(3):
            reports_grid.grid_columnconfigure(i, weight=1)

    def create_report_card(self, parent, row, col, title, description, color, command):
        """إنشاء بطاقة تقرير"""
        card_frame = tk.Frame(parent, bg=color, relief='raised', bd=2, cursor='hand2')
        card_frame.grid(row=row, column=col, padx=10, pady=10, sticky='nsew', ipadx=20, ipady=20)

        # العنوان
        title_label = tk.Label(card_frame, text=title, font=('Arial', 14, 'bold'),
                              fg='white', bg=color)
        title_label.pack(pady=(10, 5))

        # الوصف
        desc_label = tk.Label(card_frame, text=description, font=('Arial', 10),
                             fg='white', bg=color, wraplength=150)
        desc_label.pack(pady=(0, 10))

        # ربط الأحداث
        card_frame.bind("<Button-1>", lambda e: command())
        title_label.bind("<Button-1>", lambda e: command())
        desc_label.bind("<Button-1>", lambda e: command())

        # تأثير hover
        def on_enter(e):
            card_frame.config(bg=self.darken_color(color))
            title_label.config(bg=self.darken_color(color))
            desc_label.config(bg=self.darken_color(color))

        def on_leave(e):
            card_frame.config(bg=color)
            title_label.config(bg=color)
            desc_label.config(bg=color)

        card_frame.bind("<Enter>", on_enter)
        card_frame.bind("<Leave>", on_leave)
        title_label.bind("<Enter>", on_enter)
        title_label.bind("<Leave>", on_leave)
        desc_label.bind("<Enter>", on_enter)
        desc_label.bind("<Leave>", on_leave)

    def trial_balance_report(self):
        """تقرير ميزان المراجعة"""
        self.show_report_dialog("ميزان المراجعة", self.generate_trial_balance)

    def cash_boxes_report(self):
        """تقرير الصناديق"""
        self.show_report_dialog("تقرير الصناديق", self.generate_cash_boxes_report)

    def items_report(self):
        """تقرير السلع"""
        self.show_report_dialog("تقرير السلع", self.generate_items_report)

    def journal_entries_report(self):
        """تقرير القيود"""
        self.show_report_dialog("تقرير القيود المحاسبية", self.generate_journal_entries_report)

    def movement_report(self):
        """تقرير الحركة"""
        self.show_report_dialog("تقرير حركة الحسابات", self.generate_movement_report)

    def comprehensive_report(self):
        """التقرير الشامل"""
        self.show_report_dialog("التقرير الشامل", self.generate_comprehensive_report)

    def show_report_dialog(self, report_title, generate_func):
        """عرض نافذة التقرير"""
        dialog = tk.Toplevel(self.root)
        dialog.title(report_title)
        dialog.geometry("900x700")
        dialog.configure(bg='white')
        dialog.transient(self.root)
        dialog.grab_set()

        # عنوان التقرير
        title = tk.Label(dialog, text=f"📊 {report_title}",
                        font=('Arial', 16, 'bold'), bg='white', fg='#2c3e50')
        title.pack(pady=20)

        # إطار التقرير
        report_frame = tk.Frame(dialog, bg='white')
        report_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # جدول التقرير
        columns = ('العنصر', 'القيمة', 'التفاصيل')
        report_tree = ttk.Treeview(report_frame, columns=columns, show='headings')

        for col in columns:
            report_tree.heading(col, text=col)
            if col == 'القيمة':
                report_tree.column(col, width=150, anchor='e')
            else:
                report_tree.column(col, width=200, anchor='w')

        report_tree.pack(fill='both', expand=True, padx=10, pady=10)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(report_frame, orient='vertical', command=report_tree.yview)
        report_tree.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side='right', fill='y')

        # إنشاء التقرير
        data = generate_func()
        for row in data:
            report_tree.insert('', 'end', values=row)

        # أزرار العمليات
        buttons_frame = tk.Frame(dialog, bg='white')
        buttons_frame.pack(fill='x', padx=20, pady=20)

        def export_report():
            if not report_tree.get_children():
                messagebox.showwarning("تحذير", "لا توجد بيانات للتصدير")
                return

            # جمع البيانات
            data = []
            for item in report_tree.get_children():
                values = report_tree.item(item)['values']
                data.append(values)

            # إنشاء DataFrame
            df = pd.DataFrame(data, columns=['العنصر', 'القيمة', 'التفاصيل'])

            # حفظ الملف
            filename = f"{report_title.replace(' ', '_')}_{date.today().strftime('%Y%m%d')}.xlsx"
            try:
                df.to_excel(filename, index=False, engine='openpyxl')
                messagebox.showinfo("نجح", f"تم تصدير التقرير إلى {filename}")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في التصدير: {e}")

        export_btn = tk.Button(buttons_frame, text="📤 تصدير إلى Excel", command=export_report,
                              bg='#27ae60', fg='white', font=('Arial', 11, 'bold'),
                              relief='flat', padx=20, pady=8, cursor='hand2')
        export_btn.pack(side='left', padx=5)

        close_btn = tk.Button(buttons_frame, text="إغلاق", command=dialog.destroy,
                             bg='#95a5a6', fg='white', font=('Arial', 11, 'bold'),
                             relief='flat', padx=20, pady=8, cursor='hand2')
        close_btn.pack(side='right', padx=5)

    def generate_trial_balance(self):
        """إنشاء ميزان المراجعة"""
        conn = self.db.get_connection()
        query = '''
            SELECT a.account_code, a.account_name,
                   COALESCE(SUM(ed.debit_amount), 0) as total_debit,
                   COALESCE(SUM(ed.credit_amount), 0) as total_credit,
                   COALESCE(SUM(ed.debit_amount), 0) - COALESCE(SUM(ed.credit_amount), 0) as balance
            FROM accounts a
            LEFT JOIN entry_details ed ON a.account_code = ed.account_code
            GROUP BY a.account_code, a.account_name
            ORDER BY a.account_code
        '''
        df = pd.read_sql_query(query, conn)
        conn.close()

        data = []
        total_debit = 0
        total_credit = 0

        for _, row in df.iterrows():
            debit = row['total_debit']
            credit = row['total_credit']
            balance = row['balance']

            total_debit += debit
            total_credit += credit

            data.append((
                f"{row['account_code']} - {row['account_name']}",
                f"مدين: {debit:,.2f} | دائن: {credit:,.2f}",
                f"الرصيد: {balance:,.2f}"
            ))

        # إضافة الإجماليات
        data.append(("", "", ""))
        data.append(("الإجماليات", f"مدين: {total_debit:,.2f} | دائن: {total_credit:,.2f}",
                    f"الفرق: {abs(total_debit - total_credit):,.2f}"))

        return data

    def generate_cash_boxes_report(self):
        """إنشاء تقرير الصناديق"""
        boxes_df = self.db.get_cash_boxes()
        data = []
        total_balance = 0

        for _, row in boxes_df.iterrows():
            balance = row['current_balance']
            total_balance += balance

            data.append((
                f"{row['box_code']} - {row['box_name']}",
                f"{balance:,.2f}",
                f"تاريخ الإنشاء: {row['created_date'][:10]}"
            ))

        data.append(("", "", ""))
        data.append(("إجمالي الأرصدة", f"{total_balance:,.2f}", f"عدد الصناديق: {len(boxes_df)}"))

        return data

    def generate_items_report(self):
        """إنشاء تقرير السلع"""
        items_df = self.db.get_items()
        data = []
        total_value = 0

        for _, row in items_df.iterrows():
            price = row['item_price']
            total_value += price

            data.append((
                f"{row['item_code']} - {row['item_name']}",
                f"{price:,.2f}",
                row['item_description'][:50] + "..." if len(row['item_description']) > 50 else row['item_description']
            ))

        avg_price = total_value / len(items_df) if len(items_df) > 0 else 0
        data.append(("", "", ""))
        data.append(("الإحصائيات", f"متوسط السعر: {avg_price:,.2f}", f"عدد السلع: {len(items_df)}"))

        return data

    def generate_journal_entries_report(self):
        """إنشاء تقرير القيود"""
        conn = self.db.get_connection()
        query = '''
            SELECT je.entry_id, je.entry_date, je.description,
                   SUM(ed.debit_amount) as total_debit,
                   SUM(ed.credit_amount) as total_credit
            FROM journal_entries je
            LEFT JOIN entry_details ed ON je.entry_id = ed.entry_id
            GROUP BY je.entry_id, je.entry_date, je.description
            ORDER BY je.entry_id DESC
            LIMIT 20
        '''
        df = pd.read_sql_query(query, conn)
        conn.close()

        data = []
        total_debit_all = 0
        total_credit_all = 0

        for _, row in df.iterrows():
            debit = row['total_debit'] if pd.notna(row['total_debit']) else 0
            credit = row['total_credit'] if pd.notna(row['total_credit']) else 0

            total_debit_all += debit
            total_credit_all += credit

            data.append((
                f"قيد رقم {row['entry_id']} - {row['entry_date']}",
                f"مدين: {debit:,.2f} | دائن: {credit:,.2f}",
                row['description'][:50] + "..." if len(row['description']) > 50 else row['description']
            ))

        data.append(("", "", ""))
        data.append(("إجمالي آخر 20 قيد", f"مدين: {total_debit_all:,.2f} | دائن: {total_credit_all:,.2f}",
                    f"عدد القيود: {len(df)}"))

        return data

    def generate_movement_report(self):
        """إنشاء تقرير الحركة"""
        # هذا تقرير مبسط - يمكن تطويره أكثر
        data = [
            ("تقرير الحركة", "قيد التطوير", "سيتم إضافة تفاصيل الحركة قريباً"),
            ("الفترة", "آخر 30 يوم", "يمكن تخصيص الفترة"),
            ("نوع التقرير", "ملخص", "تفصيلي متاح قريباً")
        ]
        return data

    def generate_comprehensive_report(self):
        """إنشاء التقرير الشامل"""
        accounts_count = len(self.db.get_accounts())
        cash_boxes = self.db.get_cash_boxes()
        total_cash = cash_boxes['current_balance'].sum()
        items_count = len(self.db.get_items())

        # عدد القيود
        conn = self.db.get_connection()
        entries_count = pd.read_sql_query("SELECT COUNT(*) as count FROM journal_entries", conn)['count'].iloc[0]
        conn.close()

        data = [
            ("عدد الحسابات", f"{accounts_count}", "حسابات مسجلة في النظام"),
            ("عدد الصناديق", f"{len(cash_boxes)}", "صناديق نقدية"),
            ("إجمالي النقدية", f"{total_cash:,.2f}", "مجموع أرصدة الصناديق"),
            ("عدد السلع", f"{items_count}", "سلع مسجلة"),
            ("عدد القيود", f"{entries_count}", "قيود محاسبية"),
            ("", "", ""),
            ("تاريخ التقرير", date.today().strftime('%Y-%m-%d'), "تقرير شامل للنظام"),
            ("حالة النظام", "نشط", "جميع الوظائف تعمل بشكل طبيعي")
        ]

        return data

    def show_export_pro(self):
        """عرض تصدير البيانات الاحترافي"""
        self.clear_content()
        self.update_status("عرض تصدير البيانات")

        # عنوان الصفحة
        title = tk.Label(self.content_frame, text="📤 تصدير البيانات",
                        font=('Arial', 18, 'bold'), bg='white', fg='#2c3e50')
        title.pack(pady=20)

        # إطار خيارات التصدير
        export_frame = tk.LabelFrame(self.content_frame, text="خيارات التصدير",
                                    font=('Arial', 12, 'bold'), bg='white')
        export_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # شبكة خيارات التصدير
        export_grid = tk.Frame(export_frame, bg='white')
        export_grid.pack(fill='both', expand=True, padx=20, pady=20)

        # خيارات التصدير
        self.create_export_card(export_grid, 0, 0, "📋 تصدير الحسابات",
                               "تصدير جميع الحسابات إلى Excel", "#3498db", self.export_accounts)

        self.create_export_card(export_grid, 0, 1, "💰 تصدير الصناديق",
                               "تصدير أرصدة الصناديق", "#27ae60", self.export_cash_boxes)

        self.create_export_card(export_grid, 0, 2, "📦 تصدير السلع",
                               "تصدير قائمة السلع والأسعار", "#e74c3c", self.export_items)

        self.create_export_card(export_grid, 1, 0, "📝 تصدير القيود",
                               "تصدير القيود المحاسبية", "#9b59b6", self.export_journal_entries)

        self.create_export_card(export_grid, 1, 1, "📊 تصدير الكشوف",
                               "تصدير كشوف الحسابات", "#1abc9c", self.export_statements)

        self.create_export_card(export_grid, 1, 2, "📁 تصدير شامل",
                               "تصدير جميع البيانات", "#34495e", self.export_all_data)

        # تكوين الشبكة
        for i in range(3):
            export_grid.grid_columnconfigure(i, weight=1)

        # معلومات التصدير
        info_frame = tk.LabelFrame(self.content_frame, text="معلومات التصدير",
                                  font=('Arial', 12, 'bold'), bg='white')
        info_frame.pack(fill='x', padx=20, pady=10)

        info_text = """
        📌 ملاحظات مهمة:
        • يتم حفظ الملفات في نفس مجلد البرنامج
        • تنسيق الملفات: Excel (.xlsx)
        • يمكن فتح الملفات بأي برنامج جداول بيانات
        • التاريخ والوقت يتم إضافتهما لاسم الملف تلقائياً
        """

        info_label = tk.Label(info_frame, text=info_text, font=('Arial', 10),
                             bg='white', fg='#7f8c8d', justify='left')
        info_label.pack(padx=20, pady=15, anchor='w')

    def create_export_card(self, parent, row, col, title, description, color, command):
        """إنشاء بطاقة تصدير"""
        card_frame = tk.Frame(parent, bg=color, relief='raised', bd=2, cursor='hand2')
        card_frame.grid(row=row, column=col, padx=10, pady=10, sticky='nsew', ipadx=20, ipady=20)

        # العنوان
        title_label = tk.Label(card_frame, text=title, font=('Arial', 14, 'bold'),
                              fg='white', bg=color)
        title_label.pack(pady=(10, 5))

        # الوصف
        desc_label = tk.Label(card_frame, text=description, font=('Arial', 10),
                             fg='white', bg=color, wraplength=150)
        desc_label.pack(pady=(0, 10))

        # ربط الأحداث
        card_frame.bind("<Button-1>", lambda e: command())
        title_label.bind("<Button-1>", lambda e: command())
        desc_label.bind("<Button-1>", lambda e: command())

        # تأثير hover
        def on_enter(e):
            card_frame.config(bg=self.darken_color(color))
            title_label.config(bg=self.darken_color(color))
            desc_label.config(bg=self.darken_color(color))

        def on_leave(e):
            card_frame.config(bg=color)
            title_label.config(bg=color)
            desc_label.config(bg=color)

        card_frame.bind("<Enter>", on_enter)
        card_frame.bind("<Leave>", on_leave)
        title_label.bind("<Enter>", on_enter)
        title_label.bind("<Leave>", on_leave)
        desc_label.bind("<Enter>", on_enter)
        desc_label.bind("<Leave>", on_leave)

    def export_accounts(self):
        """تصدير الحسابات"""
        try:
            accounts_df = self.db.get_accounts()
            filename = f"الحسابات_{date.today().strftime('%Y%m%d_%H%M%S')}.xlsx"
            accounts_df.to_excel(filename, index=False, engine='openpyxl')
            messagebox.showinfo("نجح", f"تم تصدير {len(accounts_df)} حساب إلى {filename}")
            self.update_status(f"تم تصدير الحسابات إلى {filename}")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير الحسابات: {e}")

    def export_cash_boxes(self):
        """تصدير الصناديق"""
        try:
            boxes_df = self.db.get_cash_boxes()
            filename = f"الصناديق_{date.today().strftime('%Y%m%d_%H%M%S')}.xlsx"
            boxes_df.to_excel(filename, index=False, engine='openpyxl')
            messagebox.showinfo("نجح", f"تم تصدير {len(boxes_df)} صندوق إلى {filename}")
            self.update_status(f"تم تصدير الصناديق إلى {filename}")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير الصناديق: {e}")

    def export_items(self):
        """تصدير السلع"""
        try:
            items_df = self.db.get_items()
            filename = f"السلع_{date.today().strftime('%Y%m%d_%H%M%S')}.xlsx"
            items_df.to_excel(filename, index=False, engine='openpyxl')
            messagebox.showinfo("نجح", f"تم تصدير {len(items_df)} سلعة إلى {filename}")
            self.update_status(f"تم تصدير السلع إلى {filename}")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير السلع: {e}")

    def export_journal_entries(self):
        """تصدير القيود"""
        try:
            # الحصول على القيود مع التفاصيل
            conn = self.db.get_connection()
            query = '''
                SELECT je.entry_id, je.entry_date, je.description,
                       ed.account_code, a.account_name,
                       ed.debit_amount, ed.credit_amount
                FROM journal_entries je
                LEFT JOIN entry_details ed ON je.entry_id = ed.entry_id
                LEFT JOIN accounts a ON ed.account_code = a.account_code
                ORDER BY je.entry_id, ed.detail_id
            '''
            entries_df = pd.read_sql_query(query, conn)
            conn.close()

            filename = f"القيود_المحاسبية_{date.today().strftime('%Y%m%d_%H%M%S')}.xlsx"
            entries_df.to_excel(filename, index=False, engine='openpyxl')
            messagebox.showinfo("نجح", f"تم تصدير القيود المحاسبية إلى {filename}")
            self.update_status(f"تم تصدير القيود إلى {filename}")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير القيود: {e}")

    def export_statements(self):
        """تصدير كشوف الحسابات"""
        try:
            # إنشاء ملخص الحسابات
            conn = self.db.get_connection()
            query = '''
                SELECT a.account_code, a.account_name, a.account_type,
                       COALESCE(SUM(ed.debit_amount), 0) as total_debit,
                       COALESCE(SUM(ed.credit_amount), 0) as total_credit,
                       COALESCE(SUM(ed.debit_amount), 0) - COALESCE(SUM(ed.credit_amount), 0) as balance
                FROM accounts a
                LEFT JOIN entry_details ed ON a.account_code = ed.account_code
                GROUP BY a.account_code, a.account_name, a.account_type
                ORDER BY a.account_code
            '''
            statements_df = pd.read_sql_query(query, conn)
            conn.close()

            filename = f"كشوف_الحسابات_{date.today().strftime('%Y%m%d_%H%M%S')}.xlsx"
            statements_df.to_excel(filename, index=False, engine='openpyxl')
            messagebox.showinfo("نجح", f"تم تصدير كشوف الحسابات إلى {filename}")
            self.update_status(f"تم تصدير كشوف الحسابات إلى {filename}")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير كشوف الحسابات: {e}")

    def export_all_data(self):
        """تصدير جميع البيانات"""
        try:
            timestamp = date.today().strftime('%Y%m%d_%H%M%S')

            # تصدير كل نوع بيانات في ملف منفصل
            accounts_df = self.db.get_accounts()
            boxes_df = self.db.get_cash_boxes()
            items_df = self.db.get_items()

            # القيود مع التفاصيل
            conn = self.db.get_connection()
            entries_query = '''
                SELECT je.entry_id, je.entry_date, je.description,
                       ed.account_code, a.account_name,
                       ed.debit_amount, ed.credit_amount
                FROM journal_entries je
                LEFT JOIN entry_details ed ON je.entry_id = ed.entry_id
                LEFT JOIN accounts a ON ed.account_code = a.account_code
                ORDER BY je.entry_id, ed.detail_id
            '''
            entries_df = pd.read_sql_query(entries_query, conn)
            conn.close()

            # إنشاء ملف Excel متعدد الأوراق
            filename = f"النظام_الشامل_{timestamp}.xlsx"

            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                accounts_df.to_excel(writer, sheet_name='الحسابات', index=False)
                boxes_df.to_excel(writer, sheet_name='الصناديق', index=False)
                items_df.to_excel(writer, sheet_name='السلع', index=False)
                entries_df.to_excel(writer, sheet_name='القيود المحاسبية', index=False)

            messagebox.showinfo("نجح",
                               f"تم تصدير جميع البيانات إلى {filename}\n"
                               f"الحسابات: {len(accounts_df)}\n"
                               f"الصناديق: {len(boxes_df)}\n"
                               f"السلع: {len(items_df)}\n"
                               f"القيود: {len(entries_df)}")

            self.update_status(f"تم تصدير جميع البيانات إلى {filename}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في التصدير الشامل: {e}")

    def show_settings(self):
        """عرض الإعدادات"""
        self.clear_content()
        self.update_status("عرض الإعدادات")

        title = tk.Label(self.content_frame, text="⚙️ إعدادات النظام",
                        font=('Arial', 18, 'bold'), bg='white', fg='#2c3e50')
        title.pack(pady=30)

        # إطار الإعدادات
        settings_frame = tk.LabelFrame(self.content_frame, text="الإعدادات العامة",
                                     font=('Arial', 12, 'bold'), bg='white')
        settings_frame.pack(fill='both', expand=True, padx=30, pady=20)

        # معلومات النظام
        info_frame = tk.Frame(settings_frame, bg='white')
        info_frame.pack(fill='x', padx=20, pady=20)

        tk.Label(info_frame, text="معلومات النظام:", font=('Arial', 14, 'bold'), bg='white').pack(anchor='w')
        tk.Label(info_frame, text="الاسم: نظام المحاسبة البسيط", font=('Arial', 11), bg='white').pack(anchor='w', pady=2)
        tk.Label(info_frame, text="الإصدار: 4.0 Complete", font=('Arial', 11), bg='white').pack(anchor='w', pady=2)
        tk.Label(info_frame, text="المصمم: Hazim G. Daway", font=('Arial', 11, 'bold'), bg='white', fg='#2c3e50').pack(anchor='w', pady=2)
        tk.Label(info_frame, text="المطور التقني: Augment Agent", font=('Arial', 11), bg='white').pack(anchor='w', pady=2)

        # إحصائيات قاعدة البيانات
        stats_frame = tk.Frame(settings_frame, bg='white')
        stats_frame.pack(fill='x', padx=20, pady=20)

        tk.Label(stats_frame, text="إحصائيات قاعدة البيانات:", font=('Arial', 14, 'bold'), bg='white').pack(anchor='w')

        accounts_count = len(self.db.get_accounts())
        cash_boxes_count = len(self.db.get_cash_boxes())
        items_count = len(self.db.get_items())

        tk.Label(stats_frame, text=f"عدد الحسابات: {accounts_count}", font=('Arial', 11), bg='white').pack(anchor='w', pady=2)
        tk.Label(stats_frame, text=f"عدد الصناديق: {cash_boxes_count}", font=('Arial', 11), bg='white').pack(anchor='w', pady=2)
        tk.Label(stats_frame, text=f"عدد السلع: {items_count}", font=('Arial', 11), bg='white').pack(anchor='w', pady=2)

        # أزرار الصيانة
        maintenance_frame = tk.Frame(settings_frame, bg='white')
        maintenance_frame.pack(fill='x', padx=20, pady=20)

        tk.Label(maintenance_frame, text="صيانة النظام:", font=('Arial', 14, 'bold'), bg='white').pack(anchor='w')

        backup_btn = tk.Button(maintenance_frame, text="🗄️ نسخة احتياطية",
                              bg='#3498db', fg='white', font=('Arial', 11, 'bold'),
                              relief='flat', padx=20, pady=8, cursor='hand2')
        backup_btn.pack(side='left', padx=5, pady=10)

        optimize_btn = tk.Button(maintenance_frame, text="⚡ تحسين قاعدة البيانات",
                               bg='#27ae60', fg='white', font=('Arial', 11, 'bold'),
                               relief='flat', padx=20, pady=8, cursor='hand2')
        optimize_btn.pack(side='left', padx=5, pady=10)

def main():
    """تشغيل التطبيق الاحترافي"""
    root = tk.Tk()
    app = AccountingAppPro(root)

    # تشغيل التطبيق
    try:
        root.mainloop()
    except KeyboardInterrupt:
        print("تم إغلاق التطبيق")
    except Exception as e:
        print(f"خطأ في التطبيق: {e}")

if __name__ == "__main__":
    main()
