# دليل المستخدم - نظام المحاسبة البسيط

## نظرة عامة

نظام المحاسبة البسيط هو تطبيق محاسبي شامل مصمم خصيصاً للشركات الصغيرة والمتوسطة. يدعم النظام جميع المتطلبات الأساسية للمحاسبة مع واجهة مستخدم بسيطة وسهلة الاستخدام.

## الميزات الرئيسية

### ✅ المتطلبات المطلوبة (تم تنفيذها جميعاً):

1. **تسجيل العمليات اليومية بنظام مدين/دائن (قيود مزدوجة)** ✅
2. **إدارة الصندوق (نقدية) مع إمكانية إضافة أكثر من صندوق** ✅
3. **إضافة سلع ومصاريف مع تخصيص كود فريد لكل سلعة** ✅
4. **كشف حساب لكل سلعة أو حساب** ✅
5. **عرض رصيد الصندوق الحالي** ✅
6. **تصدير البيانات (مثل كشف الحساب أو قائمة السلع) إلى ملف Excel** ✅
7. **واجهة مستخدم بسيطة (GUI باستخدام tkinter)** ✅

## متطلبات النظام

- **نظام التشغيل**: Windows 10 أو أحدث
- **Python**: 3.7 أو أحدث (للتشغيل من المصدر)
- **الذاكرة**: 512 MB RAM كحد أدنى
- **مساحة القرص**: 100 MB مساحة فارغة

## طرق التشغيل

### الطريقة الأولى: تشغيل الملف التنفيذي (.exe)
1. قم بتشغيل `build_exe.bat` لبناء الملف التنفيذي
2. انتقل إلى مجلد `dist`
3. قم بتشغيل `نظام_المحاسبة_البسيط.exe`

### الطريقة الثانية: تشغيل من المصدر
1. قم بتشغيل `run_gui.bat`
2. أو استخدم الأمر: `python gui_main.py`

## دليل الاستخدام

### 1. لوحة التحكم 📊
- **الغرض**: عرض ملخص سريع للنظام
- **المحتويات**:
  - عدد الحسابات المسجلة
  - إجمالي النقدية في الصناديق
  - عدد السلع المسجلة
  - عدد الصناديق
  - آخر القيود المحاسبية

### 2. إدارة الحسابات 📋
- **إضافة حساب جديد**:
  1. أدخل كود الحساب (مثل: 1001)
  2. أدخل اسم الحساب (مثل: النقدية)
  3. اختر نوع الحساب من القائمة
  4. اضغط "إضافة الحساب"

- **أنواع الحسابات المتاحة**:
  - أصول
  - خصوم
  - حقوق ملكية
  - إيرادات
  - مصروفات

### 3. إدارة الصناديق 💰
- **إضافة صندوق جديد**:
  1. أدخل كود الصندوق (مثل: CASH001)
  2. أدخل اسم الصندوق (مثل: الصندوق الرئيسي)
  3. أدخل الرصيد الابتدائي
  4. اضغط "إضافة الصندوق"

- **عرض الأرصدة**:
  - يتم عرض جميع الصناديق مع أرصدتها
  - إجمالي أرصدة جميع الصناديق

### 4. إدارة السلع 📦
- **إضافة سلعة جديدة**:
  1. أدخل كود السلعة الفريد (مثل: ITEM001)
  2. أدخل اسم السلعة
  3. أدخل سعر السلعة
  4. أدخل وصف السلعة (اختياري)
  5. اضغط "إضافة السلعة"

### 5. تسجيل القيود المحاسبية 📝
- **خطوات تسجيل قيد**:
  1. أدخل تاريخ القيد
  2. أدخل وصف القيد
  3. أضف بنود القيد:
     - اختر الحساب
     - أدخل المبلغ في المدين أو الدائن
     - اضغط "إضافة بند"
  4. تأكد من توازن القيد (مجموع المدين = مجموع الدائن)
  5. اضغط "حفظ القيد"

- **ملاحظات مهمة**:
  - يجب أن يكون القيد متوازناً قبل الحفظ
  - لا يمكن إدخال مبلغ في المدين والدائن معاً لنفس البند
  - يجب إدخال بندين على الأقل

### 6. كشوف الحسابات 📊
- **عرض كشف حساب**:
  1. اختر الحساب من القائمة المنسدلة
  2. اضغط "عرض الكشف"
  3. سيتم عرض جميع الحركات مع الرصيد الجاري

### 7. التقارير 📈
- **ميزان المراجعة**:
  - عرض جميع الحسابات مع أرصدتها
  - التأكد من توازن الدفاتر

- **ملخص الحسابات**:
  - عرض جميع الحسابات مع أنواعها وأرصدتها

### 8. تصدير البيانات 📤
- **الملفات المتاحة للتصدير**:
  - تصدير الحسابات
  - تصدير السلع
  - تصدير الصناديق
  - تصدير القيود

- **خطوات التصدير**:
  1. اضغط على نوع البيانات المراد تصديرها
  2. اختر مكان الحفظ واسم الملف
  3. سيتم حفظ الملف بصيغة Excel (.xlsx)

## أمثلة عملية

### مثال 1: إيداع رأس مال نقدي
```
التاريخ: 2024-01-01
الوصف: إيداع رأس مال نقدي

البند الأول:
- الحساب: 1001 - النقدية
- مدين: 50,000
- دائن: 0

البند الثاني:
- الحساب: 3001 - رأس المال
- مدين: 0
- دائن: 50,000
```

### مثال 2: شراء بضاعة نقداً
```
التاريخ: 2024-01-02
الوصف: شراء بضاعة نقداً

البند الأول:
- الحساب: 5001 - المشتريات
- مدين: 10,000
- دائن: 0

البند الثاني:
- الحساب: 1001 - النقدية
- مدين: 0
- دائن: 10,000
```

## نصائح مهمة

### ✅ أفضل الممارسات:
1. **النسخ الاحتياطي**: قم بتصدير البيانات بانتظام
2. **التحقق من التوازن**: راجع ميزان المراجعة دورياً
3. **الأكواد المنطقية**: استخدم أكواد منطقية للحسابات والسلع
4. **الوصف الواضح**: اكتب أوصاف واضحة للقيود

### ⚠️ تحذيرات:
1. تأكد من توازن القيود قبل الحفظ
2. لا تستخدم أكواد مكررة للحسابات أو السلع
3. احتفظ بنسخة احتياطية من ملف قاعدة البيانات

## حل المشاكل الشائعة

### مشكلة: لا يمكن حفظ القيد
**الحل**: تأكد من أن مجموع المدين = مجموع الدائن

### مشكلة: خطأ في إضافة حساب
**الحل**: تأكد من أن كود الحساب غير مستخدم مسبقاً

### مشكلة: فشل في تصدير البيانات
**الحل**: تأكد من أن لديك صلاحيات الكتابة في المجلد المختار

## الدعم الفني

للحصول على المساعدة:
1. راجع هذا الدليل أولاً
2. تحقق من رسائل الخطأ المعروضة
3. تأكد من صحة البيانات المدخلة

## معلومات إضافية

- **قاعدة البيانات**: SQLite (ملف accounting.db)
- **تنسيق التصدير**: Excel (.xlsx)
- **الترميز**: UTF-8 (دعم اللغة العربية)
- **النسخة**: 1.0

---

**تم تطوير هذا النظام خصيصاً لتلبية جميع متطلبات المحاسبة الأساسية مع سهولة الاستخدام والموثوقية.**
