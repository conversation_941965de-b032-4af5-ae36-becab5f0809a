#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام المحاسبة البسيط - واجهة رسومية
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import pandas as pd
from datetime import datetime, date
from database import AccountingDatabase
from utils import *
import os
import sys

class AccountingApp:
    def __init__(self, root):
        self.root = root
        self.root.title("نظام المحاسبة البسيط")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f0f0f0')
        
        # تهيئة قاعدة البيانات
        try:
            self.db = AccountingDatabase()
            print("✅ تم تهيئة قاعدة البيانات بنجاح")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تهيئة قاعدة البيانات: {e}")
            sys.exit(1)
        
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان الرئيسي
        title_frame = tk.Frame(self.root, bg='#2c3e50', height=80)
        title_frame.pack(fill='x', padx=10, pady=5)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text="💰 نظام المحاسبة البسيط", 
                              font=('Arial', 20, 'bold'), fg='white', bg='#2c3e50')
        title_label.pack(expand=True)
        
        # إطار القوائم الجانبية والمحتوى
        main_frame = tk.Frame(self.root, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # القائمة الجانبية
        sidebar_frame = tk.Frame(main_frame, bg='#34495e', width=250)
        sidebar_frame.pack(side='left', fill='y', padx=(0, 10))
        sidebar_frame.pack_propagate(False)
        
        # أزرار القائمة
        menu_buttons = [
            ("📊 لوحة التحكم", self.show_dashboard),
            ("📋 إدارة الحسابات", self.show_accounts),
            ("💰 إدارة الصناديق", self.show_cash_boxes),
            ("📦 إدارة السلع", self.show_items),
            ("📝 تسجيل القيود", self.show_journal_entry),
            ("📊 كشوف الحسابات", self.show_statements),
            ("📈 التقارير", self.show_reports),
            ("📤 تصدير البيانات", self.show_export)
        ]
        
        for text, command in menu_buttons:
            btn = tk.Button(sidebar_frame, text=text, command=command,
                           font=('Arial', 12), bg='#3498db', fg='white',
                           relief='flat', pady=10, width=25)
            btn.pack(fill='x', padx=10, pady=5)
        
        # منطقة المحتوى الرئيسي
        self.content_frame = tk.Frame(main_frame, bg='white', relief='raised', bd=1)
        self.content_frame.pack(side='right', fill='both', expand=True)
        
        # عرض لوحة التحكم افتراضياً
        self.show_dashboard()
        
    def clear_content(self):
        """مسح المحتوى الحالي"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()
    
    def show_dashboard(self):
        """عرض لوحة التحكم"""
        self.clear_content()
        
        # عنوان الصفحة
        title = tk.Label(self.content_frame, text="📊 لوحة التحكم", 
                        font=('Arial', 18, 'bold'), bg='white')
        title.pack(pady=20)
        
        # إطار الإحصائيات
        stats_frame = tk.Frame(self.content_frame, bg='white')
        stats_frame.pack(fill='x', padx=20, pady=10)
        
        # الحصول على الإحصائيات
        accounts_count = len(self.db.get_accounts())
        cash_boxes = self.db.get_cash_boxes()
        total_cash = cash_boxes['current_balance'].sum()
        items_count = len(self.db.get_items())
        
        # عرض الإحصائيات
        stats = [
            ("عدد الحسابات", accounts_count, "#3498db"),
            ("إجمالي النقدية", f"{total_cash:,.2f}", "#27ae60"),
            ("عدد السلع", items_count, "#e74c3c"),
            ("الصناديق", len(cash_boxes), "#f39c12")
        ]
        
        for i, (label, value, color) in enumerate(stats):
            stat_frame = tk.Frame(stats_frame, bg=color, relief='raised', bd=2)
            stat_frame.grid(row=0, column=i, padx=10, pady=10, sticky='ew')
            stats_frame.grid_columnconfigure(i, weight=1)
            
            tk.Label(stat_frame, text=str(value), font=('Arial', 16, 'bold'), 
                    fg='white', bg=color).pack(pady=5)
            tk.Label(stat_frame, text=label, font=('Arial', 10), 
                    fg='white', bg=color).pack(pady=5)
        
        # آخر القيود
        recent_frame = tk.LabelFrame(self.content_frame, text="آخر القيود المحاسبية", 
                                   font=('Arial', 12, 'bold'), bg='white')
        recent_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # جدول آخر القيود
        columns = ('رقم القيد', 'التاريخ', 'الوصف')
        tree = ttk.Treeview(recent_frame, columns=columns, show='headings', height=10)
        
        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=150)
        
        # الحصول على آخر القيود
        conn = self.db.get_connection()
        recent_entries = pd.read_sql_query(
            "SELECT entry_id, entry_date, description FROM journal_entries ORDER BY entry_id DESC LIMIT 10", 
            conn
        )
        conn.close()
        
        for _, row in recent_entries.iterrows():
            tree.insert('', 'end', values=(row['entry_id'], row['entry_date'], row['description']))
        
        tree.pack(fill='both', expand=True, padx=10, pady=10)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(recent_frame, orient='vertical', command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side='right', fill='y')
    
    def show_accounts(self):
        """عرض إدارة الحسابات"""
        self.clear_content()
        
        title = tk.Label(self.content_frame, text="📋 إدارة الحسابات", 
                        font=('Arial', 18, 'bold'), bg='white')
        title.pack(pady=20)
        
        # إطار الإدخال
        input_frame = tk.LabelFrame(self.content_frame, text="إضافة حساب جديد", 
                                   font=('Arial', 12, 'bold'), bg='white')
        input_frame.pack(fill='x', padx=20, pady=10)
        
        # حقول الإدخال
        tk.Label(input_frame, text="كود الحساب:", bg='white').grid(row=0, column=0, padx=10, pady=5, sticky='w')
        account_code_entry = tk.Entry(input_frame, width=20)
        account_code_entry.grid(row=0, column=1, padx=10, pady=5)
        
        tk.Label(input_frame, text="اسم الحساب:", bg='white').grid(row=0, column=2, padx=10, pady=5, sticky='w')
        account_name_entry = tk.Entry(input_frame, width=30)
        account_name_entry.grid(row=0, column=3, padx=10, pady=5)
        
        tk.Label(input_frame, text="نوع الحساب:", bg='white').grid(row=1, column=0, padx=10, pady=5, sticky='w')
        account_type_combo = ttk.Combobox(input_frame, values=get_account_types(), width=18)
        account_type_combo.grid(row=1, column=1, padx=10, pady=5)
        
        def add_account():
            code = account_code_entry.get().strip()
            name = account_name_entry.get().strip()
            acc_type = account_type_combo.get().strip()
            
            if not code or not name or not acc_type:
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول")
                return
            
            if self.db.add_account(code, name, acc_type):
                messagebox.showinfo("نجح", "تم إضافة الحساب بنجاح")
                account_code_entry.delete(0, tk.END)
                account_name_entry.delete(0, tk.END)
                account_type_combo.set('')
                refresh_accounts()
            else:
                messagebox.showerror("خطأ", "كود الحساب موجود مسبقاً")
        
        add_btn = tk.Button(input_frame, text="إضافة الحساب", command=add_account,
                           bg='#27ae60', fg='white', font=('Arial', 10, 'bold'))
        add_btn.grid(row=1, column=2, columnspan=2, padx=10, pady=10)
        
        # جدول الحسابات
        accounts_frame = tk.LabelFrame(self.content_frame, text="قائمة الحسابات", 
                                     font=('Arial', 12, 'bold'), bg='white')
        accounts_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        columns = ('كود الحساب', 'اسم الحساب', 'نوع الحساب')
        accounts_tree = ttk.Treeview(accounts_frame, columns=columns, show='headings')
        
        for col in columns:
            accounts_tree.heading(col, text=col)
            accounts_tree.column(col, width=200)
        
        def refresh_accounts():
            # مسح البيانات الحالية
            for item in accounts_tree.get_children():
                accounts_tree.delete(item)
            
            # إضافة البيانات الجديدة
            accounts_df = self.db.get_accounts()
            for _, row in accounts_df.iterrows():
                accounts_tree.insert('', 'end', values=(
                    row['account_code'], row['account_name'], row['account_type']
                ))
        
        refresh_accounts()
        accounts_tree.pack(fill='both', expand=True, padx=10, pady=10)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(accounts_frame, orient='vertical', command=accounts_tree.yview)
        accounts_tree.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side='right', fill='y')
    
    def show_cash_boxes(self):
        """عرض إدارة الصناديق"""
        self.clear_content()
        
        title = tk.Label(self.content_frame, text="💰 إدارة الصناديق", 
                        font=('Arial', 18, 'bold'), bg='white')
        title.pack(pady=20)
        
        # إطار الإدخال
        input_frame = tk.LabelFrame(self.content_frame, text="إضافة صندوق جديد", 
                                   font=('Arial', 12, 'bold'), bg='white')
        input_frame.pack(fill='x', padx=20, pady=10)
        
        # حقول الإدخال
        tk.Label(input_frame, text="كود الصندوق:", bg='white').grid(row=0, column=0, padx=10, pady=5, sticky='w')
        box_code_entry = tk.Entry(input_frame, width=20)
        box_code_entry.grid(row=0, column=1, padx=10, pady=5)
        
        tk.Label(input_frame, text="اسم الصندوق:", bg='white').grid(row=0, column=2, padx=10, pady=5, sticky='w')
        box_name_entry = tk.Entry(input_frame, width=30)
        box_name_entry.grid(row=0, column=3, padx=10, pady=5)
        
        tk.Label(input_frame, text="الرصيد الابتدائي:", bg='white').grid(row=1, column=0, padx=10, pady=5, sticky='w')
        balance_entry = tk.Entry(input_frame, width=20)
        balance_entry.insert(0, "0.0")
        balance_entry.grid(row=1, column=1, padx=10, pady=5)
        
        def add_cash_box():
            code = box_code_entry.get().strip()
            name = box_name_entry.get().strip()
            try:
                balance = float(balance_entry.get().strip())
            except ValueError:
                messagebox.showerror("خطأ", "الرصيد الابتدائي يجب أن يكون رقماً")
                return
            
            if not code or not name:
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول")
                return
            
            if self.db.add_cash_box(code, name, balance):
                messagebox.showinfo("نجح", "تم إضافة الصندوق بنجاح")
                box_code_entry.delete(0, tk.END)
                box_name_entry.delete(0, tk.END)
                balance_entry.delete(0, tk.END)
                balance_entry.insert(0, "0.0")
                refresh_cash_boxes()
            else:
                messagebox.showerror("خطأ", "كود الصندوق موجود مسبقاً")
        
        add_btn = tk.Button(input_frame, text="إضافة الصندوق", command=add_cash_box,
                           bg='#27ae60', fg='white', font=('Arial', 10, 'bold'))
        add_btn.grid(row=1, column=2, columnspan=2, padx=10, pady=10)
        
        # جدول الصناديق
        boxes_frame = tk.LabelFrame(self.content_frame, text="قائمة الصناديق", 
                                   font=('Arial', 12, 'bold'), bg='white')
        boxes_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        columns = ('كود الصندوق', 'اسم الصندوق', 'الرصيد الحالي')
        boxes_tree = ttk.Treeview(boxes_frame, columns=columns, show='headings')
        
        for col in columns:
            boxes_tree.heading(col, text=col)
            boxes_tree.column(col, width=200)
        
        def refresh_cash_boxes():
            # مسح البيانات الحالية
            for item in boxes_tree.get_children():
                boxes_tree.delete(item)
            
            # إضافة البيانات الجديدة
            boxes_df = self.db.get_cash_boxes()
            total_balance = 0
            for _, row in boxes_df.iterrows():
                balance = row['current_balance']
                total_balance += balance
                boxes_tree.insert('', 'end', values=(
                    row['box_code'], row['box_name'], f"{balance:,.2f}"
                ))
            
            # عرض الإجمالي
            total_label.config(text=f"إجمالي أرصدة الصناديق: {total_balance:,.2f}")
        
        refresh_cash_boxes()
        boxes_tree.pack(fill='both', expand=True, padx=10, pady=10)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(boxes_frame, orient='vertical', command=boxes_tree.yview)
        boxes_tree.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side='right', fill='y')
        
        # إجمالي الأرصدة
        total_label = tk.Label(self.content_frame, text="", font=('Arial', 12, 'bold'), bg='white')
        total_label.pack(pady=10)
        refresh_cash_boxes()  # لتحديث الإجمالي

    def show_items(self):
        """عرض إدارة السلع"""
        self.clear_content()

        title = tk.Label(self.content_frame, text="📦 إدارة السلع",
                        font=('Arial', 18, 'bold'), bg='white')
        title.pack(pady=20)

        # إطار الإدخال
        input_frame = tk.LabelFrame(self.content_frame, text="إضافة سلعة جديدة",
                                   font=('Arial', 12, 'bold'), bg='white')
        input_frame.pack(fill='x', padx=20, pady=10)

        # حقول الإدخال
        tk.Label(input_frame, text="كود السلعة:", bg='white').grid(row=0, column=0, padx=10, pady=5, sticky='w')
        item_code_entry = tk.Entry(input_frame, width=20)
        item_code_entry.grid(row=0, column=1, padx=10, pady=5)

        tk.Label(input_frame, text="اسم السلعة:", bg='white').grid(row=0, column=2, padx=10, pady=5, sticky='w')
        item_name_entry = tk.Entry(input_frame, width=30)
        item_name_entry.grid(row=0, column=3, padx=10, pady=5)

        tk.Label(input_frame, text="سعر السلعة:", bg='white').grid(row=1, column=0, padx=10, pady=5, sticky='w')
        price_entry = tk.Entry(input_frame, width=20)
        price_entry.insert(0, "0.0")
        price_entry.grid(row=1, column=1, padx=10, pady=5)

        tk.Label(input_frame, text="وصف السلعة:", bg='white').grid(row=1, column=2, padx=10, pady=5, sticky='w')
        desc_entry = tk.Entry(input_frame, width=30)
        desc_entry.grid(row=1, column=3, padx=10, pady=5)

        def add_item():
            code = item_code_entry.get().strip()
            name = item_name_entry.get().strip()
            desc = desc_entry.get().strip()
            try:
                price = float(price_entry.get().strip())
            except ValueError:
                messagebox.showerror("خطأ", "السعر يجب أن يكون رقماً")
                return

            if not code or not name:
                messagebox.showerror("خطأ", "يرجى ملء الحقول المطلوبة")
                return

            if self.db.add_item(code, name, price, desc):
                messagebox.showinfo("نجح", "تم إضافة السلعة بنجاح")
                item_code_entry.delete(0, tk.END)
                item_name_entry.delete(0, tk.END)
                price_entry.delete(0, tk.END)
                price_entry.insert(0, "0.0")
                desc_entry.delete(0, tk.END)
                refresh_items()
            else:
                messagebox.showerror("خطأ", "كود السلعة موجود مسبقاً")

        add_btn = tk.Button(input_frame, text="إضافة السلعة", command=add_item,
                           bg='#27ae60', fg='white', font=('Arial', 10, 'bold'))
        add_btn.grid(row=2, column=0, columnspan=4, padx=10, pady=10)

        # جدول السلع
        items_frame = tk.LabelFrame(self.content_frame, text="قائمة السلع",
                                   font=('Arial', 12, 'bold'), bg='white')
        items_frame.pack(fill='both', expand=True, padx=20, pady=10)

        columns = ('كود السلعة', 'اسم السلعة', 'السعر', 'الوصف')
        items_tree = ttk.Treeview(items_frame, columns=columns, show='headings')

        for col in columns:
            items_tree.heading(col, text=col)
            items_tree.column(col, width=150)

        def refresh_items():
            # مسح البيانات الحالية
            for item in items_tree.get_children():
                items_tree.delete(item)

            # إضافة البيانات الجديدة
            items_df = self.db.get_items()
            for _, row in items_df.iterrows():
                items_tree.insert('', 'end', values=(
                    row['item_code'], row['item_name'],
                    f"{row['item_price']:,.2f}", row['item_description']
                ))

        refresh_items()
        items_tree.pack(fill='both', expand=True, padx=10, pady=10)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(items_frame, orient='vertical', command=items_tree.yview)
        items_tree.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side='right', fill='y')

    def show_journal_entry(self):
        """عرض تسجيل القيود"""
        self.clear_content()

        title = tk.Label(self.content_frame, text="📝 تسجيل القيود المحاسبية",
                        font=('Arial', 18, 'bold'), bg='white')
        title.pack(pady=20)

        # إطار معلومات القيد
        entry_info_frame = tk.LabelFrame(self.content_frame, text="معلومات القيد",
                                        font=('Arial', 12, 'bold'), bg='white')
        entry_info_frame.pack(fill='x', padx=20, pady=10)

        tk.Label(entry_info_frame, text="تاريخ القيد:", bg='white').grid(row=0, column=0, padx=10, pady=5, sticky='w')
        date_entry = tk.Entry(entry_info_frame, width=20)
        date_entry.insert(0, date.today().strftime('%Y-%m-%d'))
        date_entry.grid(row=0, column=1, padx=10, pady=5)

        tk.Label(entry_info_frame, text="وصف القيد:", bg='white').grid(row=0, column=2, padx=10, pady=5, sticky='w')
        desc_entry = tk.Entry(entry_info_frame, width=40)
        desc_entry.grid(row=0, column=3, padx=10, pady=5)

        # إطار تفاصيل القيد
        details_frame = tk.LabelFrame(self.content_frame, text="تفاصيل القيد",
                                     font=('Arial', 12, 'bold'), bg='white')
        details_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # جدول تفاصيل القيد
        columns = ('الحساب', 'مدين', 'دائن')
        details_tree = ttk.Treeview(details_frame, columns=columns, show='headings', height=8)

        for col in columns:
            details_tree.heading(col, text=col)
            details_tree.column(col, width=200)

        details_tree.pack(fill='both', expand=True, padx=10, pady=10)

        # إطار إضافة بند
        add_detail_frame = tk.Frame(details_frame, bg='white')
        add_detail_frame.pack(fill='x', padx=10, pady=5)

        # الحصول على قائمة الحسابات
        accounts_df = self.db.get_accounts()
        account_options = [f"{row['account_code']} - {row['account_name']}" for _, row in accounts_df.iterrows()]

        tk.Label(add_detail_frame, text="الحساب:", bg='white').grid(row=0, column=0, padx=5, pady=5)
        account_combo = ttk.Combobox(add_detail_frame, values=account_options, width=30)
        account_combo.grid(row=0, column=1, padx=5, pady=5)

        tk.Label(add_detail_frame, text="مدين:", bg='white').grid(row=0, column=2, padx=5, pady=5)
        debit_entry = tk.Entry(add_detail_frame, width=15)
        debit_entry.insert(0, "0.0")
        debit_entry.grid(row=0, column=3, padx=5, pady=5)

        tk.Label(add_detail_frame, text="دائن:", bg='white').grid(row=0, column=4, padx=5, pady=5)
        credit_entry = tk.Entry(add_detail_frame, width=15)
        credit_entry.insert(0, "0.0")
        credit_entry.grid(row=0, column=5, padx=5, pady=5)

        def add_detail():
            account = account_combo.get().strip()
            if not account:
                messagebox.showerror("خطأ", "يرجى اختيار حساب")
                return

            try:
                debit = float(debit_entry.get().strip())
                credit = float(credit_entry.get().strip())
            except ValueError:
                messagebox.showerror("خطأ", "المبالغ يجب أن تكون أرقاماً")
                return

            if debit == 0 and credit == 0:
                messagebox.showerror("خطأ", "يجب إدخال مبلغ في المدين أو الدائن")
                return

            if debit > 0 and credit > 0:
                messagebox.showerror("خطأ", "لا يمكن إدخال مبلغ في المدين والدائن معاً")
                return

            details_tree.insert('', 'end', values=(account, f"{debit:,.2f}" if debit > 0 else "",
                                                 f"{credit:,.2f}" if credit > 0 else ""))

            # مسح الحقول
            account_combo.set('')
            debit_entry.delete(0, tk.END)
            debit_entry.insert(0, "0.0")
            credit_entry.delete(0, tk.END)
            credit_entry.insert(0, "0.0")

            update_totals()

        add_detail_btn = tk.Button(add_detail_frame, text="إضافة بند", command=add_detail,
                                  bg='#3498db', fg='white', font=('Arial', 10, 'bold'))
        add_detail_btn.grid(row=0, column=6, padx=10, pady=5)

        # إطار المجاميع
        totals_frame = tk.Frame(self.content_frame, bg='white')
        totals_frame.pack(fill='x', padx=20, pady=10)

        total_debit_label = tk.Label(totals_frame, text="مجموع المدين: 0.00",
                                    font=('Arial', 12, 'bold'), bg='white')
        total_debit_label.grid(row=0, column=0, padx=20)

        total_credit_label = tk.Label(totals_frame, text="مجموع الدائن: 0.00",
                                     font=('Arial', 12, 'bold'), bg='white')
        total_credit_label.grid(row=0, column=1, padx=20)

        balance_label = tk.Label(totals_frame, text="الفرق: 0.00",
                                font=('Arial', 12, 'bold'), bg='white')
        balance_label.grid(row=0, column=2, padx=20)

        def update_totals():
            total_debit = 0
            total_credit = 0

            for item in details_tree.get_children():
                values = details_tree.item(item)['values']
                if len(values) >= 3:
                    debit_str = values[1].replace(',', '') if values[1] else "0"
                    credit_str = values[2].replace(',', '') if values[2] else "0"

                    try:
                        total_debit += float(debit_str)
                        total_credit += float(credit_str)
                    except ValueError:
                        continue

            difference = abs(total_debit - total_credit)

            total_debit_label.config(text=f"مجموع المدين: {total_debit:,.2f}")
            total_credit_label.config(text=f"مجموع الدائن: {total_credit:,.2f}")
            balance_label.config(text=f"الفرق: {difference:,.2f}")

            # تغيير لون الفرق
            if difference < 0.01:
                balance_label.config(fg='green')
                save_btn.config(state='normal')
            else:
                balance_label.config(fg='red')
                save_btn.config(state='disabled')

        def save_entry():
            entry_date = date_entry.get().strip()
            description = desc_entry.get().strip()

            if not entry_date or not description:
                messagebox.showerror("خطأ", "يرجى ملء تاريخ ووصف القيد")
                return

            # جمع تفاصيل القيد
            details = []
            for item in details_tree.get_children():
                values = details_tree.item(item)['values']
                if len(values) >= 3:
                    account_text = values[0]
                    account_code = account_text.split(' - ')[0]

                    debit_str = values[1].replace(',', '') if values[1] else "0"
                    credit_str = values[2].replace(',', '') if values[2] else "0"

                    try:
                        debit = float(debit_str)
                        credit = float(credit_str)

                        details.append({
                            'account_code': account_code,
                            'debit': debit,
                            'credit': credit
                        })
                    except ValueError:
                        continue

            if len(details) < 2:
                messagebox.showerror("خطأ", "يجب إدخال بندين على الأقل")
                return

            if self.db.add_journal_entry(entry_date, description, details):
                messagebox.showinfo("نجح", "تم حفظ القيد بنجاح")
                # مسح النموذج
                desc_entry.delete(0, tk.END)
                for item in details_tree.get_children():
                    details_tree.delete(item)
                update_totals()
            else:
                messagebox.showerror("خطأ", "فشل في حفظ القيد")

        # أزرار التحكم
        buttons_frame = tk.Frame(self.content_frame, bg='white')
        buttons_frame.pack(fill='x', padx=20, pady=10)

        save_btn = tk.Button(buttons_frame, text="حفظ القيد", command=save_entry,
                            bg='#27ae60', fg='white', font=('Arial', 12, 'bold'),
                            state='disabled')
        save_btn.pack(side='left', padx=10)

        def clear_entry():
            desc_entry.delete(0, tk.END)
            for item in details_tree.get_children():
                details_tree.delete(item)
            update_totals()

        clear_btn = tk.Button(buttons_frame, text="مسح", command=clear_entry,
                             bg='#e74c3c', fg='white', font=('Arial', 12, 'bold'))
        clear_btn.pack(side='left', padx=10)

    def show_statements(self):
        """عرض كشوف الحسابات"""
        self.clear_content()

        title = tk.Label(self.content_frame, text="📊 كشوف الحسابات",
                        font=('Arial', 18, 'bold'), bg='white')
        title.pack(pady=20)

        # اختيار الحساب
        select_frame = tk.Frame(self.content_frame, bg='white')
        select_frame.pack(fill='x', padx=20, pady=10)

        tk.Label(select_frame, text="اختر الحساب:", font=('Arial', 12, 'bold'), bg='white').pack(side='left', padx=10)

        accounts_df = self.db.get_accounts()
        account_options = [f"{row['account_code']} - {row['account_name']}" for _, row in accounts_df.iterrows()]

        account_var = tk.StringVar()
        account_combo = ttk.Combobox(select_frame, textvariable=account_var, values=account_options, width=40)
        account_combo.pack(side='left', padx=10)

        def show_statement():
            selected = account_var.get()
            if not selected:
                messagebox.showerror("خطأ", "يرجى اختيار حساب")
                return

            account_code = selected.split(' - ')[0]
            account_name = selected.split(' - ')[1]

            # مسح الجدول الحالي
            for item in statement_tree.get_children():
                statement_tree.delete(item)

            # الحصول على كشف الحساب
            statement_df = self.db.get_account_statement(account_code)

            if not statement_df.empty:
                for _, row in statement_df.iterrows():
                    debit_str = f"{row['debit_amount']:,.2f}" if row['debit_amount'] > 0 else ""
                    credit_str = f"{row['credit_amount']:,.2f}" if row['credit_amount'] > 0 else ""
                    balance_str = f"{row['running_balance']:,.2f}"

                    statement_tree.insert('', 'end', values=(
                        row['entry_date'], row['description'], debit_str, credit_str, balance_str
                    ))

                # عرض الرصيد النهائي
                final_balance = statement_df['running_balance'].iloc[-1]
                balance_label.config(text=f"الرصيد النهائي: {final_balance:,.2f}")
            else:
                balance_label.config(text="لا توجد حركات على هذا الحساب")

        show_btn = tk.Button(select_frame, text="عرض الكشف", command=show_statement,
                            bg='#3498db', fg='white', font=('Arial', 10, 'bold'))
        show_btn.pack(side='left', padx=10)

        # جدول كشف الحساب
        statement_frame = tk.LabelFrame(self.content_frame, text="كشف الحساب",
                                       font=('Arial', 12, 'bold'), bg='white')
        statement_frame.pack(fill='both', expand=True, padx=20, pady=10)

        columns = ('التاريخ', 'الوصف', 'مدين', 'دائن', 'الرصيد')
        statement_tree = ttk.Treeview(statement_frame, columns=columns, show='headings')

        for col in columns:
            statement_tree.heading(col, text=col)
            statement_tree.column(col, width=150)

        statement_tree.pack(fill='both', expand=True, padx=10, pady=10)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(statement_frame, orient='vertical', command=statement_tree.yview)
        statement_tree.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side='right', fill='y')

        # الرصيد النهائي
        balance_label = tk.Label(self.content_frame, text="", font=('Arial', 12, 'bold'), bg='white')
        balance_label.pack(pady=10)

    def show_reports(self):
        """عرض التقارير"""
        self.clear_content()

        title = tk.Label(self.content_frame, text="📈 التقارير",
                        font=('Arial', 18, 'bold'), bg='white')
        title.pack(pady=20)

        # أزرار التقارير
        reports_frame = tk.Frame(self.content_frame, bg='white')
        reports_frame.pack(fill='x', padx=20, pady=10)

        def show_trial_balance():
            # مسح المحتوى الحالي
            for widget in report_content_frame.winfo_children():
                widget.destroy()

            # عنوان التقرير
            report_title = tk.Label(report_content_frame, text="ميزان المراجعة",
                                   font=('Arial', 14, 'bold'), bg='white')
            report_title.pack(pady=10)

            # جدول ميزان المراجعة
            columns = ('كود الحساب', 'اسم الحساب', 'مدين', 'دائن')
            trial_tree = ttk.Treeview(report_content_frame, columns=columns, show='headings')

            for col in columns:
                trial_tree.heading(col, text=col)
                trial_tree.column(col, width=150)

            # الحصول على ميزان المراجعة
            trial_balance_df = get_trial_balance(self.db)

            for _, row in trial_balance_df.iterrows():
                debit_str = f"{row['مدين']:,.2f}" if row['مدين'] > 0 else ""
                credit_str = f"{row['دائن']:,.2f}" if row['دائن'] > 0 else ""

                trial_tree.insert('', 'end', values=(
                    row['كود الحساب'], row['اسم الحساب'], debit_str, credit_str
                ))

            trial_tree.pack(fill='both', expand=True, padx=10, pady=10)

        def show_summary():
            # مسح المحتوى الحالي
            for widget in report_content_frame.winfo_children():
                widget.destroy()

            # عنوان التقرير
            report_title = tk.Label(report_content_frame, text="ملخص الحسابات",
                                   font=('Arial', 14, 'bold'), bg='white')
            report_title.pack(pady=10)

            # جدول ملخص الحسابات
            columns = ('كود الحساب', 'اسم الحساب', 'نوع الحساب', 'الرصيد')
            summary_tree = ttk.Treeview(report_content_frame, columns=columns, show='headings')

            for col in columns:
                summary_tree.heading(col, text=col)
                summary_tree.column(col, width=150)

            # الحصول على ملخص الحسابات
            summary_df = create_summary_report(self.db)

            for _, row in summary_df.iterrows():
                summary_tree.insert('', 'end', values=(
                    row['كود الحساب'], row['اسم الحساب'],
                    row['نوع الحساب'], f"{row['الرصيد']:,.2f}"
                ))

            summary_tree.pack(fill='both', expand=True, padx=10, pady=10)

        trial_btn = tk.Button(reports_frame, text="ميزان المراجعة", command=show_trial_balance,
                             bg='#3498db', fg='white', font=('Arial', 12, 'bold'))
        trial_btn.pack(side='left', padx=10)

        summary_btn = tk.Button(reports_frame, text="ملخص الحسابات", command=show_summary,
                               bg='#27ae60', fg='white', font=('Arial', 12, 'bold'))
        summary_btn.pack(side='left', padx=10)

        # منطقة عرض التقرير
        report_content_frame = tk.Frame(self.content_frame, bg='white', relief='sunken', bd=1)
        report_content_frame.pack(fill='both', expand=True, padx=20, pady=10)

    def show_export(self):
        """عرض تصدير البيانات"""
        self.clear_content()

        title = tk.Label(self.content_frame, text="📤 تصدير البيانات",
                        font=('Arial', 18, 'bold'), bg='white')
        title.pack(pady=20)

        # أزرار التصدير
        export_frame = tk.Frame(self.content_frame, bg='white')
        export_frame.pack(fill='x', padx=20, pady=20)

        def export_accounts():
            try:
                filename = filedialog.asksaveasfilename(
                    defaultextension=".xlsx",
                    filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                    title="حفظ ملف الحسابات"
                )
                if filename:
                    accounts_df = self.db.get_accounts()
                    accounts_df.to_excel(filename, index=False, sheet_name='الحسابات')
                    messagebox.showinfo("نجح", f"تم تصدير الحسابات إلى:\n{filename}")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تصدير الحسابات:\n{e}")

        def export_items():
            try:
                filename = filedialog.asksaveasfilename(
                    defaultextension=".xlsx",
                    filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                    title="حفظ ملف السلع"
                )
                if filename:
                    items_df = self.db.get_items()
                    items_df.to_excel(filename, index=False, sheet_name='السلع')
                    messagebox.showinfo("نجح", f"تم تصدير السلع إلى:\n{filename}")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تصدير السلع:\n{e}")

        def export_cash_boxes():
            try:
                filename = filedialog.asksaveasfilename(
                    defaultextension=".xlsx",
                    filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                    title="حفظ ملف الصناديق"
                )
                if filename:
                    boxes_df = self.db.get_cash_boxes()
                    boxes_df.to_excel(filename, index=False, sheet_name='الصناديق')
                    messagebox.showinfo("نجح", f"تم تصدير الصناديق إلى:\n{filename}")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تصدير الصناديق:\n{e}")

        def export_entries():
            try:
                filename = filedialog.asksaveasfilename(
                    defaultextension=".xlsx",
                    filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                    title="حفظ ملف القيود"
                )
                if filename:
                    conn = self.db.get_connection()
                    entries_df = pd.read_sql_query("""
                        SELECT je.entry_id, je.entry_date, je.description,
                               ed.account_code, a.account_name,
                               ed.debit_amount, ed.credit_amount
                        FROM journal_entries je
                        JOIN entry_details ed ON je.entry_id = ed.entry_id
                        JOIN accounts a ON ed.account_code = a.account_code
                        ORDER BY je.entry_id, ed.detail_id
                    """, conn)
                    conn.close()

                    entries_df.to_excel(filename, index=False, sheet_name='القيود')
                    messagebox.showinfo("نجح", f"تم تصدير القيود إلى:\n{filename}")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تصدير القيود:\n{e}")

        # أزرار التصدير
        export_buttons = [
            ("تصدير الحسابات", export_accounts, "#3498db"),
            ("تصدير السلع", export_items, "#27ae60"),
            ("تصدير الصناديق", export_cash_boxes, "#f39c12"),
            ("تصدير القيود", export_entries, "#e74c3c")
        ]

        for i, (text, command, color) in enumerate(export_buttons):
            btn = tk.Button(export_frame, text=text, command=command,
                           bg=color, fg='white', font=('Arial', 12, 'bold'),
                           width=20, height=3)
            btn.grid(row=i//2, column=i%2, padx=20, pady=20)

        # معلومات إضافية
        info_label = tk.Label(self.content_frame,
                             text="سيتم حفظ الملفات بصيغة Excel (.xlsx)\nيمكنك اختيار مكان الحفظ واسم الملف",
                             font=('Arial', 10), bg='white', fg='gray')
        info_label.pack(pady=20)

def main():
    """تشغيل التطبيق الرئيسي"""
    root = tk.Tk()
    app = AccountingApp(root)

    # تشغيل التطبيق
    try:
        root.mainloop()
    except KeyboardInterrupt:
        print("تم إغلاق التطبيق")
    except Exception as e:
        print(f"خطأ في التطبيق: {e}")

if __name__ == "__main__":
    main()
