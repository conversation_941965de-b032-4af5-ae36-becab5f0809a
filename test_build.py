#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط لبناء الملف التنفيذي
"""

import tkinter as tk
from tkinter import messagebox

def test_app():
    root = tk.Tk()
    root.title("اختبار نظام المحاسبة")
    root.geometry("400x300")
    
    label = tk.Label(root, text="مرحباً بك في نظام المحاسبة البسيط", 
                    font=('Arial', 14), pady=20)
    label.pack()
    
    def show_message():
        messagebox.showinfo("نجح", "النظام يعمل بشكل صحيح!")
    
    button = tk.Button(root, text="اختبار", command=show_message,
                      font=('Arial', 12), bg='#3498db', fg='white')
    button.pack(pady=20)
    
    root.mainloop()

if __name__ == "__main__":
    test_app()
