import sqlite3
import pandas as pd
from datetime import datetime
import os

class AccountingDatabase:
    def __init__(self, db_path="accounting.db"):
        self.db_path = db_path
        self.init_database()
    
    def get_connection(self):
        return sqlite3.connect(self.db_path)
    
    def init_database(self):
        """إنشاء جداول قاعدة البيانات"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # جدول الحسابات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS accounts (
                account_code TEXT PRIMARY KEY,
                account_name TEXT NOT NULL,
                account_type TEXT NOT NULL,
                created_date TEXT DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول الصناديق
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS cash_boxes (
                box_code TEXT PRIMARY KEY,
                box_name TEXT NOT NULL,
                current_balance REAL DEFAULT 0,
                created_date TEXT DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول السلع
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS items (
                item_code TEXT PRIMARY KEY,
                item_name TEXT NOT NULL,
                item_price REAL DEFAULT 0,
                item_description TEXT,
                created_date TEXT DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول القيود المحاسبية
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS journal_entries (
                entry_id INTEGER PRIMARY KEY AUTOINCREMENT,
                entry_date TEXT NOT NULL,
                description TEXT NOT NULL,
                created_date TEXT DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول تفاصيل القيود
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS entry_details (
                detail_id INTEGER PRIMARY KEY AUTOINCREMENT,
                entry_id INTEGER,
                account_code TEXT,
                debit_amount REAL DEFAULT 0,
                credit_amount REAL DEFAULT 0,
                FOREIGN KEY (entry_id) REFERENCES journal_entries (entry_id),
                FOREIGN KEY (account_code) REFERENCES accounts (account_code)
            )
        ''')
        
        conn.commit()
        conn.close()
        
        # إضافة بيانات افتراضية
        self.add_default_data()
    
    def add_default_data(self):
        """إضافة بيانات افتراضية للنظام"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # التحقق من وجود بيانات
        cursor.execute("SELECT COUNT(*) FROM accounts")
        if cursor.fetchone()[0] == 0:
            # إضافة حسابات افتراضية
            default_accounts = [
                ('1001', 'النقدية', 'أصول'),
                ('1002', 'البنك', 'أصول'),
                ('2001', 'الموردون', 'خصوم'),
                ('3001', 'رأس المال', 'حقوق ملكية'),
                ('4001', 'المبيعات', 'إيرادات'),
                ('5001', 'المشتريات', 'مصروفات'),
                ('5002', 'مصروفات عمومية', 'مصروفات')
            ]
            
            cursor.executemany(
                "INSERT INTO accounts (account_code, account_name, account_type) VALUES (?, ?, ?)",
                default_accounts
            )
            
            # إضافة صندوق افتراضي
            cursor.execute(
                "INSERT INTO cash_boxes (box_code, box_name, current_balance) VALUES (?, ?, ?)",
                ('CASH001', 'الصندوق الرئيسي', 0)
            )
        
        conn.commit()
        conn.close()
    
    def add_account(self, code, name, account_type):
        """إضافة حساب جديد"""
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute(
                "INSERT INTO accounts (account_code, account_name, account_type) VALUES (?, ?, ?)",
                (code, name, account_type)
            )
            conn.commit()
            return True
        except sqlite3.IntegrityError:
            return False
        finally:
            conn.close()
    
    def add_cash_box(self, code, name, initial_balance=0):
        """إضافة صندوق جديد"""
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute(
                "INSERT INTO cash_boxes (box_code, box_name, current_balance) VALUES (?, ?, ?)",
                (code, name, initial_balance)
            )
            conn.commit()
            return True
        except sqlite3.IntegrityError:
            return False
        finally:
            conn.close()
    
    def add_item(self, code, name, price, description=""):
        """إضافة سلعة جديدة"""
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute(
                "INSERT INTO items (item_code, item_name, item_price, item_description) VALUES (?, ?, ?, ?)",
                (code, name, price, description)
            )
            conn.commit()
            return True
        except sqlite3.IntegrityError:
            return False
        finally:
            conn.close()
    
    def add_journal_entry(self, date, description, details):
        """إضافة قيد محاسبي مع التفاصيل"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # إضافة القيد الرئيسي
            cursor.execute(
                "INSERT INTO journal_entries (entry_date, description) VALUES (?, ?)",
                (date, description)
            )
            entry_id = cursor.lastrowid
            
            # إضافة تفاصيل القيد
            for detail in details:
                cursor.execute(
                    "INSERT INTO entry_details (entry_id, account_code, debit_amount, credit_amount) VALUES (?, ?, ?, ?)",
                    (entry_id, detail['account_code'], detail['debit'], detail['credit'])
                )
            
            conn.commit()
            return True
        except Exception as e:
            conn.rollback()
            print(f"خطأ في إضافة القيد: {e}")
            return False
        finally:
            conn.close()
    
    def get_accounts(self):
        """الحصول على جميع الحسابات"""
        conn = self.get_connection()
        df = pd.read_sql_query("SELECT * FROM accounts ORDER BY account_code", conn)
        conn.close()
        return df
    
    def get_cash_boxes(self):
        """الحصول على جميع الصناديق"""
        conn = self.get_connection()
        df = pd.read_sql_query("SELECT * FROM cash_boxes ORDER BY box_code", conn)
        conn.close()
        return df
    
    def get_items(self):
        """الحصول على جميع السلع"""
        conn = self.get_connection()
        df = pd.read_sql_query("SELECT * FROM items ORDER BY item_code", conn)
        conn.close()
        return df
    
    def get_account_statement(self, account_code):
        """كشف حساب لحساب معين"""
        conn = self.get_connection()
        query = '''
            SELECT 
                je.entry_date,
                je.description,
                ed.debit_amount,
                ed.credit_amount,
                (SELECT SUM(debit_amount - credit_amount) 
                 FROM entry_details ed2 
                 JOIN journal_entries je2 ON ed2.entry_id = je2.entry_id
                 WHERE ed2.account_code = ? AND je2.entry_date <= je.entry_date) as running_balance
            FROM entry_details ed
            JOIN journal_entries je ON ed.entry_id = je.entry_id
            WHERE ed.account_code = ?
            ORDER BY je.entry_date, je.entry_id
        '''
        df = pd.read_sql_query(query, conn, params=[account_code, account_code])
        conn.close()
        return df
    
    def get_cash_box_balance(self, box_code):
        """الحصول على رصيد صندوق معين"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT current_balance FROM cash_boxes WHERE box_code = ?", (box_code,))
        result = cursor.fetchone()
        conn.close()
        return result[0] if result else 0

    # ==================== وظائف التحديث ====================

    def update_account(self, old_code, new_code, name, account_type):
        """تحديث حساب موجود"""
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute(
                "UPDATE accounts SET account_code = ?, account_name = ?, account_type = ? WHERE account_code = ?",
                (new_code, name, account_type, old_code)
            )
            # تحديث المراجع في جدول تفاصيل القيود
            if old_code != new_code:
                cursor.execute(
                    "UPDATE entry_details SET account_code = ? WHERE account_code = ?",
                    (new_code, old_code)
                )
            conn.commit()
            return True
        except sqlite3.IntegrityError:
            return False
        finally:
            conn.close()

    def update_cash_box(self, old_code, new_code, name, balance):
        """تحديث صندوق موجود"""
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute(
                "UPDATE cash_boxes SET box_code = ?, box_name = ?, current_balance = ? WHERE box_code = ?",
                (new_code, name, balance, old_code)
            )
            conn.commit()
            return True
        except sqlite3.IntegrityError:
            return False
        finally:
            conn.close()

    def update_item(self, old_code, new_code, name, price, description):
        """تحديث سلعة موجودة"""
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute(
                "UPDATE items SET item_code = ?, item_name = ?, item_price = ?, item_description = ? WHERE item_code = ?",
                (new_code, name, price, description, old_code)
            )
            conn.commit()
            return True
        except sqlite3.IntegrityError:
            return False
        finally:
            conn.close()

    # ==================== وظائف الحذف ====================

    def delete_account(self, account_code):
        """حذف حساب (مع التحقق من الاستخدام)"""
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            # التحقق من وجود قيود مرتبطة بالحساب
            cursor.execute("SELECT COUNT(*) FROM entry_details WHERE account_code = ?", (account_code,))
            if cursor.fetchone()[0] > 0:
                return False, "لا يمكن حذف الحساب لوجود قيود مرتبطة به"

            cursor.execute("DELETE FROM accounts WHERE account_code = ?", (account_code,))
            conn.commit()
            return True, "تم حذف الحساب بنجاح"
        except Exception as e:
            return False, f"خطأ في حذف الحساب: {e}"
        finally:
            conn.close()

    def delete_cash_box(self, box_code):
        """حذف صندوق"""
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute("DELETE FROM cash_boxes WHERE box_code = ?", (box_code,))
            conn.commit()
            return True, "تم حذف الصندوق بنجاح"
        except Exception as e:
            return False, f"خطأ في حذف الصندوق: {e}"
        finally:
            conn.close()

    def delete_item(self, item_code):
        """حذف سلعة"""
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            cursor.execute("DELETE FROM items WHERE item_code = ?", (item_code,))
            conn.commit()
            return True, "تم حذف السلعة بنجاح"
        except Exception as e:
            return False, f"خطأ في حذف السلعة: {e}"
        finally:
            conn.close()

    def delete_journal_entry(self, entry_id):
        """حذف قيد محاسبي مع تفاصيله"""
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            # حذف تفاصيل القيد أولاً
            cursor.execute("DELETE FROM entry_details WHERE entry_id = ?", (entry_id,))
            # حذف القيد الرئيسي
            cursor.execute("DELETE FROM journal_entries WHERE entry_id = ?", (entry_id,))
            conn.commit()
            return True, "تم حذف القيد بنجاح"
        except Exception as e:
            conn.rollback()
            return False, f"خطأ في حذف القيد: {e}"
        finally:
            conn.close()

    # ==================== وظائف مساعدة ====================

    def get_account_by_code(self, account_code):
        """الحصول على تفاصيل حساب معين"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM accounts WHERE account_code = ?", (account_code,))
        result = cursor.fetchone()
        conn.close()
        return result

    def get_cash_box_by_code(self, box_code):
        """الحصول على تفاصيل صندوق معين"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM cash_boxes WHERE box_code = ?", (box_code,))
        result = cursor.fetchone()
        conn.close()
        return result

    def get_item_by_code(self, item_code):
        """الحصول على تفاصيل سلعة معينة"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM items WHERE item_code = ?", (item_code,))
        result = cursor.fetchone()
        conn.close()
        return result

    def get_journal_entry_details(self, entry_id):
        """الحصول على تفاصيل قيد معين"""
        conn = self.get_connection()
        query = '''
            SELECT je.entry_id, je.entry_date, je.description,
                   ed.detail_id, ed.account_code, a.account_name,
                   ed.debit_amount, ed.credit_amount
            FROM journal_entries je
            JOIN entry_details ed ON je.entry_id = ed.entry_id
            JOIN accounts a ON ed.account_code = a.account_code
            WHERE je.entry_id = ?
            ORDER BY ed.detail_id
        '''
        df = pd.read_sql_query(query, conn, params=[entry_id])
        conn.close()
        return df
