import streamlit as st
import pandas as pd
from datetime import datetime, date
from database import AccountingDatabase
from utils import *

# إعداد الصفحة
st.set_page_config(
    page_title="نظام المحاسبة البسيط",
    page_icon="💰",
    layout="wide",
    initial_sidebar_state="expanded"
)

# تهيئة قاعدة البيانات
@st.cache_resource
def init_database():
    return AccountingDatabase()

db = init_database()

# العنوان الرئيسي
st.title("💰 نظام المحاسبة البسيط")
st.markdown("---")

# الشريط الجانبي للتنقل
st.sidebar.title("القائمة الرئيسية")
page = st.sidebar.selectbox(
    "اختر الصفحة:",
    [
        "لوحة التحكم",
        "إدارة الحسابات", 
        "إدارة الصناديق",
        "إدارة السلع",
        "تسجيل القيود",
        "كشوف الحسابات",
        "التقارير",
        "تصدير البيانات"
    ]
)

# لوحة التحكم
if page == "لوحة التحكم":
    st.header("📊 لوحة التحكم")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        accounts_count = len(db.get_accounts())
        st.metric("عدد الحسابات", accounts_count)
    
    with col2:
        cash_boxes = db.get_cash_boxes()
        total_cash = cash_boxes['current_balance'].sum()
        st.metric("إجمالي النقدية", f"{format_currency(total_cash)}")
    
    with col3:
        items_count = len(db.get_items())
        st.metric("عدد السلع", items_count)
    
    with col4:
        # عدد القيود اليوم
        today = date.today().strftime('%Y-%m-%d')
        conn = db.get_connection()
        today_entries = pd.read_sql_query(
            "SELECT COUNT(*) as count FROM journal_entries WHERE entry_date = ?", 
            conn, params=[today]
        )
        conn.close()
        st.metric("قيود اليوم", today_entries['count'].iloc[0])
    
    st.markdown("---")
    
    # عرض آخر القيود
    st.subheader("آخر القيود المحاسبية")
    conn = db.get_connection()
    recent_entries = pd.read_sql_query(
        """SELECT entry_id, entry_date, description 
           FROM journal_entries 
           ORDER BY entry_id DESC 
           LIMIT 5""", 
        conn
    )
    conn.close()
    
    if not recent_entries.empty:
        st.dataframe(recent_entries, use_container_width=True)
    else:
        st.info("لا توجد قيود محاسبية بعد")

# إدارة الحسابات
elif page == "إدارة الحسابات":
    st.header("📋 إدارة الحسابات")
    
    tab1, tab2 = st.tabs(["عرض الحسابات", "إضافة حساب جديد"])
    
    with tab1:
        st.subheader("قائمة الحسابات")
        
        # البحث
        search_term = st.text_input("البحث في الحسابات:")
        accounts_df = search_accounts(db, search_term)
        
        if not accounts_df.empty:
            st.dataframe(accounts_df, use_container_width=True)
        else:
            st.info("لا توجد حسابات")
    
    with tab2:
        st.subheader("إضافة حساب جديد")
        
        with st.form("add_account_form"):
            col1, col2 = st.columns(2)
            
            with col1:
                account_code = st.text_input("كود الحساب*")
                account_name = st.text_input("اسم الحساب*")
            
            with col2:
                account_type = st.selectbox("نوع الحساب*", get_account_types())
            
            submitted = st.form_submit_button("إضافة الحساب")
            
            if submitted:
                if account_code and account_name:
                    if db.add_account(account_code, account_name, account_type):
                        st.success("تم إضافة الحساب بنجاح!")
                        st.rerun()
                    else:
                        st.error("كود الحساب موجود مسبقاً!")
                else:
                    st.error("يرجى ملء جميع الحقول المطلوبة")

# إدارة الصناديق
elif page == "إدارة الصناديق":
    st.header("💰 إدارة الصناديق")
    
    tab1, tab2 = st.tabs(["عرض الصناديق", "إضافة صندوق جديد"])
    
    with tab1:
        st.subheader("قائمة الصناديق")
        cash_boxes_df = db.get_cash_boxes()
        
        if not cash_boxes_df.empty:
            # تنسيق العرض
            display_df = cash_boxes_df.copy()
            display_df['current_balance'] = display_df['current_balance'].apply(format_currency)
            st.dataframe(display_df, use_container_width=True)
            
            # عرض إجمالي الأرصدة
            total_balance = cash_boxes_df['current_balance'].sum()
            st.metric("إجمالي أرصدة الصناديق", format_currency(total_balance))
        else:
            st.info("لا توجد صناديق")
    
    with tab2:
        st.subheader("إضافة صندوق جديد")
        
        with st.form("add_cash_box_form"):
            col1, col2 = st.columns(2)
            
            with col1:
                box_code = st.text_input("كود الصندوق*")
                box_name = st.text_input("اسم الصندوق*")
            
            with col2:
                initial_balance = st.number_input("الرصيد الابتدائي", min_value=0.0, value=0.0)
            
            submitted = st.form_submit_button("إضافة الصندوق")
            
            if submitted:
                if box_code and box_name:
                    if db.add_cash_box(box_code, box_name, initial_balance):
                        st.success("تم إضافة الصندوق بنجاح!")
                        st.rerun()
                    else:
                        st.error("كود الصندوق موجود مسبقاً!")
                else:
                    st.error("يرجى ملء جميع الحقول المطلوبة")

# تسجيل القيود
elif page == "تسجيل القيود":
    st.header("📝 تسجيل القيود المحاسبية")

    with st.form("journal_entry_form"):
        col1, col2 = st.columns(2)

        with col1:
            entry_date = st.date_input("تاريخ القيد", value=date.today())

        with col2:
            description = st.text_input("وصف القيد*")

        st.subheader("تفاصيل القيد")

        # الحصول على قائمة الحسابات
        accounts_df = db.get_accounts()
        account_options = [f"{row['account_code']} - {row['account_name']}" for _, row in accounts_df.iterrows()]

        # إنشاء جدول لإدخال التفاصيل
        num_lines = st.number_input("عدد بنود القيد", min_value=2, max_value=10, value=2)

        details = []
        total_debit = 0
        total_credit = 0

        for i in range(num_lines):
            st.write(f"**البند {i+1}:**")
            col1, col2, col3 = st.columns(3)

            with col1:
                selected_account = st.selectbox(f"الحساب {i+1}", account_options, key=f"account_{i}")
                account_code = selected_account.split(' - ')[0] if selected_account else ""

            with col2:
                debit = st.number_input(f"مدين {i+1}", min_value=0.0, value=0.0, key=f"debit_{i}")
                total_debit += debit

            with col3:
                credit = st.number_input(f"دائن {i+1}", min_value=0.0, value=0.0, key=f"credit_{i}")
                total_credit += credit

            if account_code and (debit > 0 or credit > 0):
                details.append({
                    'account_code': account_code,
                    'debit': debit,
                    'credit': credit
                })

        # عرض المجاميع
        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("مجموع المدين", format_currency(total_debit))
        with col2:
            st.metric("مجموع الدائن", format_currency(total_credit))
        with col3:
            difference = abs(total_debit - total_credit)
            st.metric("الفرق", format_currency(difference))

        # التحقق من توازن القيد
        is_balanced = abs(total_debit - total_credit) < 0.01
        if not is_balanced and (total_debit > 0 or total_credit > 0):
            st.error("⚠️ القيد غير متوازن! يجب أن يكون مجموع المدين = مجموع الدائن")

        submitted = st.form_submit_button("حفظ القيد", disabled=not is_balanced)

        if submitted and is_balanced:
            if description and details:
                if db.add_journal_entry(entry_date.strftime('%Y-%m-%d'), description, details):
                    st.success("تم حفظ القيد بنجاح!")
                    st.rerun()
                else:
                    st.error("حدث خطأ في حفظ القيد")
            else:
                st.error("يرجى ملء جميع الحقول المطلوبة")

# كشوف الحسابات
elif page == "كشوف الحسابات":
    st.header("📊 كشوف الحسابات")

    # اختيار الحساب
    accounts_df = db.get_accounts()
    if not accounts_df.empty:
        account_options = [f"{row['account_code']} - {row['account_name']}" for _, row in accounts_df.iterrows()]
        selected_account = st.selectbox("اختر الحساب:", account_options)

        if selected_account:
            account_code = selected_account.split(' - ')[0]
            account_name = selected_account.split(' - ')[1]

            st.subheader(f"كشف حساب: {account_name}")

            # الحصول على كشف الحساب
            statement_df = db.get_account_statement(account_code)

            if not statement_df.empty:
                # تنسيق البيانات للعرض
                display_df = statement_df.copy()
                display_df['entry_date'] = pd.to_datetime(display_df['entry_date']).dt.strftime('%d/%m/%Y')
                display_df['debit_amount'] = display_df['debit_amount'].apply(lambda x: format_currency(x) if x > 0 else "")
                display_df['credit_amount'] = display_df['credit_amount'].apply(lambda x: format_currency(x) if x > 0 else "")
                display_df['running_balance'] = display_df['running_balance'].apply(format_currency)

                # إعادة تسمية الأعمدة
                display_df.columns = ['التاريخ', 'الوصف', 'مدين', 'دائن', 'الرصيد']

                st.dataframe(display_df, use_container_width=True)

                # عرض الرصيد النهائي
                final_balance = statement_df['running_balance'].iloc[-1]
                st.metric("الرصيد النهائي", format_currency(final_balance))

                # زر تصدير كشف الحساب
                if st.button("تصدير كشف الحساب إلى Excel"):
                    filename = f"كشف_حساب_{account_code}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
                    if export_to_excel(display_df, filename):
                        st.success(f"تم تصدير الملف: {filename}")
            else:
                st.info("لا توجد حركات على هذا الحساب")
    else:
        st.info("لا توجد حسابات في النظام")

# التقارير
elif page == "التقارير":
    st.header("📈 التقارير")

    tab1, tab2, tab3 = st.tabs(["ميزان المراجعة", "ملخص الحسابات", "تقرير الصناديق"])

    with tab1:
        st.subheader("ميزان المراجعة")
        trial_balance_df = get_trial_balance(db)

        if not trial_balance_df.empty:
            # تنسيق البيانات
            display_df = trial_balance_df.copy()
            display_df['مدين'] = display_df['مدين'].apply(lambda x: format_currency(x) if x > 0 else "")
            display_df['دائن'] = display_df['دائن'].apply(lambda x: format_currency(x) if x > 0 else "")

            st.dataframe(display_df, use_container_width=True)

            if st.button("تصدير ميزان المراجعة"):
                filename = f"ميزان_المراجعة_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
                if export_to_excel(display_df, filename):
                    st.success(f"تم تصدير الملف: {filename}")

    with tab2:
        st.subheader("ملخص الحسابات")
        summary_df = create_summary_report(db)

        if not summary_df.empty:
            # تنسيق البيانات
            display_df = summary_df.copy()
            display_df['الرصيد'] = display_df['الرصيد'].apply(format_currency)

            st.dataframe(display_df, use_container_width=True)

            if st.button("تصدير ملخص الحسابات"):
                filename = f"ملخص_الحسابات_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
                if export_to_excel(display_df, filename):
                    st.success(f"تم تصدير الملف: {filename}")

    with tab3:
        st.subheader("تقرير الصناديق")
        cash_boxes_df = db.get_cash_boxes()

        if not cash_boxes_df.empty:
            # تنسيق البيانات
            display_df = cash_boxes_df.copy()
            display_df['current_balance'] = display_df['current_balance'].apply(format_currency)
            display_df.columns = ['كود الصندوق', 'اسم الصندوق', 'الرصيد الحالي', 'تاريخ الإنشاء']

            st.dataframe(display_df, use_container_width=True)

            # إجمالي الأرصدة
            total_cash = cash_boxes_df['current_balance'].sum()
            st.metric("إجمالي النقدية", format_currency(total_cash))

            if st.button("تصدير تقرير الصناديق"):
                filename = f"تقرير_الصناديق_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
                if export_to_excel(display_df, filename):
                    st.success(f"تم تصدير الملف: {filename}")

# تصدير البيانات
elif page == "تصدير البيانات":
    st.header("📤 تصدير البيانات")

    st.subheader("اختر البيانات المراد تصديرها:")

    col1, col2 = st.columns(2)

    with col1:
        if st.button("تصدير جميع الحسابات"):
            accounts_df = db.get_accounts()
            filename = f"الحسابات_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            if export_to_excel(accounts_df, filename):
                st.success(f"تم تصدير الملف: {filename}")

        if st.button("تصدير جميع السلع"):
            items_df = db.get_items()
            filename = f"السلع_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            if export_to_excel(items_df, filename):
                st.success(f"تم تصدير الملف: {filename}")

    with col2:
        if st.button("تصدير جميع الصناديق"):
            cash_boxes_df = db.get_cash_boxes()
            filename = f"الصناديق_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            if export_to_excel(cash_boxes_df, filename):
                st.success(f"تم تصدير الملف: {filename}")

        if st.button("تصدير جميع القيود"):
            conn = db.get_connection()
            entries_df = pd.read_sql_query("""
                SELECT je.entry_id, je.entry_date, je.description,
                       ed.account_code, a.account_name,
                       ed.debit_amount, ed.credit_amount
                FROM journal_entries je
                JOIN entry_details ed ON je.entry_id = ed.entry_id
                JOIN accounts a ON ed.account_code = a.account_code
                ORDER BY je.entry_id, ed.detail_id
            """, conn)
            conn.close()

            filename = f"القيود_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            if export_to_excel(entries_df, filename):
                st.success(f"تم تصدير الملف: {filename}")

# تذييل الصفحة
st.markdown("---")
st.markdown("**نظام المحاسبة البسيط** - تم تطويره باستخدام Python و Streamlit")
