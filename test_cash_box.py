#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مخصص لمشكلة إضافة الصندوق
"""

from database import AccountingDatabase
import pandas as pd

def test_cash_box_operations():
    """اختبار عمليات الصندوق"""
    print("🧪 اختبار عمليات الصندوق...")
    
    # إنشاء قاعدة البيانات
    db = AccountingDatabase("test_cash_box.db")
    print("✅ تم إنشاء قاعدة البيانات")
    
    # عرض الصناديق الحالية
    print("\n📋 الصناديق الحالية:")
    current_boxes = db.get_cash_boxes()
    print(current_boxes)
    
    # اختبار إضافة صندوق جديد
    print("\n🔧 اختبار إضافة صندوق جديد...")
    
    test_cases = [
        ("CASH002", "صندوق المبيعات", 1000.0),
        ("CASH003", "صندوق المشتريات", 500.0),
        ("CASH004", "صندوق الطوارئ", 0.0),
        ("CASH001", "صندوق مكرر", 100.0)  # هذا يجب أن يفشل
    ]
    
    for code, name, balance in test_cases:
        print(f"\n🧪 اختبار إضافة: {code} - {name} - {balance}")
        
        try:
            result = db.add_cash_box(code, name, balance)
            if result:
                print(f"✅ نجح: تم إضافة {code}")
            else:
                print(f"❌ فشل: لم يتم إضافة {code} (ربما مكرر)")
        except Exception as e:
            print(f"💥 خطأ: {e}")
    
    # عرض الصناديق بعد الإضافة
    print("\n📋 الصناديق بعد الإضافة:")
    updated_boxes = db.get_cash_boxes()
    print(updated_boxes)
    
    # اختبار تفصيلي لوظيفة add_cash_box
    print("\n🔍 اختبار تفصيلي...")
    
    # اختبار مع بيانات صحيحة
    test_code = "TEST001"
    test_name = "صندوق اختبار"
    test_balance = 250.0
    
    print(f"محاولة إضافة: {test_code} - {test_name} - {test_balance}")
    
    # التحقق من عدم وجود الكود مسبقاً
    conn = db.get_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT COUNT(*) FROM cash_boxes WHERE box_code = ?", (test_code,))
    count_before = cursor.fetchone()[0]
    print(f"عدد الصناديق بنفس الكود قبل الإضافة: {count_before}")
    
    # محاولة الإضافة
    result = db.add_cash_box(test_code, test_name, test_balance)
    print(f"نتيجة الإضافة: {result}")
    
    # التحقق بعد الإضافة
    cursor.execute("SELECT COUNT(*) FROM cash_boxes WHERE box_code = ?", (test_code,))
    count_after = cursor.fetchone()[0]
    print(f"عدد الصناديق بنفس الكود بعد الإضافة: {count_after}")
    
    # عرض الصندوق المضاف
    cursor.execute("SELECT * FROM cash_boxes WHERE box_code = ?", (test_code,))
    added_box = cursor.fetchone()
    if added_box:
        print(f"الصندوق المضاف: {added_box}")
    else:
        print("لم يتم العثور على الصندوق المضاف!")
    
    conn.close()
    
    # اختبار مع بيانات خاطئة
    print("\n🚫 اختبار مع بيانات خاطئة...")
    
    # كود فارغ
    result1 = db.add_cash_box("", "اسم صحيح", 100)
    print(f"كود فارغ: {result1}")
    
    # اسم فارغ
    result2 = db.add_cash_box("VALID001", "", 100)
    print(f"اسم فارغ: {result2}")
    
    # رصيد سالب
    result3 = db.add_cash_box("VALID002", "اسم صحيح", -100)
    print(f"رصيد سالب: {result3}")
    
    print("\n🎉 انتهى الاختبار!")

if __name__ == "__main__":
    test_cash_box_operations()
